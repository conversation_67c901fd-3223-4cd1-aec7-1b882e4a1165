
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SupabaseProvider } from "./contexts/SupabaseContext";
import { Layout } from "./components/Layout";
import ProjectOverview from "./pages/ProjectOverview";
import TableEditor from "./pages/TableEditor";
import Database from "./pages/Database";
import Workspaces from "./pages/Workspaces";
import NotFound from "./pages/NotFound";
import SupabaseSettings from "./pages/SupabaseSettings";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <SupabaseProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Layout>
            <Routes>
              <Route path="/" element={<ProjectOverview />} />
              <Route path="/table-editor" element={<TableEditor />} />
              <Route path="/database" element={<Database />} />
              <Route path="/workspaces" element={<Workspaces />} />
              <Route path="/sql-editor" element={<div className="text-center p-8 text-muted-foreground">SQL Editor - Coming Soon</div>} />
              <Route path="/authentication" element={<div className="text-center p-8 text-muted-foreground">Authentication - Coming Soon</div>} />
              <Route path="/storage" element={<div className="text-center p-8 text-muted-foreground">Storage - Coming Soon</div>} />
              <Route path="/realtime" element={<div className="text-center p-8 text-muted-foreground">Realtime - Coming Soon</div>} />
              <Route path="/api-docs" element={<div className="text-center p-8 text-muted-foreground">API Documentation - Coming Soon</div>} />
              <Route path="/logs" element={<div className="text-center p-8 text-muted-foreground">Logs - Coming Soon</div>} />
              <Route path="/settings" element={<SupabaseSettings />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Layout>
        </BrowserRouter>
      </TooltipProvider>
    </SupabaseProvider>
  </QueryClientProvider>
);

export default App;
