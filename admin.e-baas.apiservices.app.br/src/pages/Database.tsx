import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Database as DatabaseIcon, 
  Table, 
  Key, 
  Activity,
  Clock,
  TrendingUp
} from "lucide-react";
import { useSupabase } from "@/contexts/SupabaseContext";
import { usePostgrest } from "@/hooks/usePostgrest";
import { SupabaseConfig } from "@/components/SupabaseConfig";
import { useToast } from "@/hooks/use-toast";

interface DatabaseStats {
  tableCount: number;
  totalRows: number;
  databaseSize: string;
}

export default function Database() {
  const [stats, setStats] = useState<DatabaseStats>({ tableCount: 0, totalRows: 0, databaseSize: "0 MB" });
  const [loading, setLoading] = useState(false);
  const { isConfigured, config } = useSupabase();
  const { select } = usePostgrest();
  const { toast } = useToast();

  const fetchDatabaseStats = async () => {
    if (!isConfigured) return;
    
    setLoading(true);
    try {
      // Get table count using PostgREST
      const tables = await select('information_schema.tables', {
        filter: { table_schema: 'public' },
        select: 'table_name'
      });

      setStats({
        tableCount: tables?.length || 0,
        totalRows: 0, // This would require more complex queries
        databaseSize: "Calculando..." // This would require admin access
      });
    } catch (error: any) {
      console.error('Error fetching database stats:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar estatísticas: " + error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isConfigured) {
      fetchDatabaseStats();
    }
  }, [isConfigured]);

  if (!isConfigured) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight">Database</h1>
          <p className="text-muted-foreground mt-2">
            Configure o Supabase para começar
          </p>
        </div>
        <SupabaseConfig />
      </div>
    );
  }

  const connectionInfo = {
    host: config?.url?.replace('https://', '').replace('http://', '') || '',
    port: "5432",
    database: "postgres",
    username: "postgres",
    ssl: "require"
  };

  const recentQueries = [
    { query: "SELECT * FROM information_schema.tables", time: "2 min ago", duration: "145ms" },
    { query: "SELECT count(*) FROM auth.users", time: "5 min ago", duration: "89ms" },
    { query: "SHOW pg_database_size(current_database())", time: "12 min ago", duration: "203ms" },
    { query: "SELECT schemaname, tablename FROM pg_tables", time: "1 hour ago", duration: "67ms" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Database</h1>
          <p className="text-muted-foreground mt-2">
            Monitore e gerencie seu banco PostgreSQL
          </p>
        </div>
        <Button className="bg-primary hover:bg-primary/90" onClick={fetchDatabaseStats} disabled={loading}>
          <DatabaseIcon className="w-4 h-4 mr-2" />
          {loading ? "Atualizando..." : "Atualizar Stats"}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="gradient-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total de Tabelas
            </CardTitle>
            <Table className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.tableCount}</div>
            <p className="text-xs text-green-400 mt-1">tabelas no schema public</p>
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Performance
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">142ms</div>
            <p className="text-xs text-green-400 mt-1">tempo médio de resposta</p>
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Tamanho do Banco
            </CardTitle>
            <DatabaseIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.databaseSize}</div>
            <p className="text-xs text-muted-foreground mt-1">espaço usado</p>
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Status
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Online</div>
            <p className="text-xs text-green-400 mt-1">conectado e funcionando</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Connection Information</CardTitle>
            <CardDescription>
              Database connection details for your application
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(connectionInfo).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center p-3 rounded-lg glass-effect">
                <span className="text-sm font-medium capitalize">{key}:</span>
                <code className="text-sm bg-muted px-2 py-1 rounded">{value}</code>
              </div>
            ))}
            <Button variant="outline" className="w-full">
              Copy Connection String
            </Button>
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Recent Queries</CardTitle>
            <CardDescription>
              Latest database operations and their performance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentQueries.map((query, index) => (
              <div key={index} className="p-3 rounded-lg glass-effect">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline" className="text-xs">
                    {query.duration}
                  </Badge>
                  <span className="text-xs text-muted-foreground">{query.time}</span>
                </div>
                <code className="text-sm text-muted-foreground block overflow-hidden text-ellipsis">
                  {query.query}
                </code>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      <Card className="gradient-border">
        <CardHeader>
          <CardTitle>Database Tools</CardTitle>
          <CardDescription>
            Essential tools for database management and monitoring
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <div className="flex items-center space-x-2 mb-1">
                  <Table className="w-4 h-4" />
                  <p className="font-medium">Query Editor</p>
                </div>
                <p className="text-sm text-muted-foreground">Execute SQL queries directly</p>
              </div>
            </Button>
            
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <div className="flex items-center space-x-2 mb-1">
                  <Key className="w-4 h-4" />
                  <p className="font-medium">Backup & Restore</p>
                </div>
                <p className="text-sm text-muted-foreground">Manage database backups</p>
              </div>
            </Button>
            
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <div className="flex items-center space-x-2 mb-1">
                  <Clock className="w-4 h-4" />
                  <p className="font-medium">Performance Insights</p>
                </div>
                <p className="text-sm text-muted-foreground">Analyze query performance</p>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
