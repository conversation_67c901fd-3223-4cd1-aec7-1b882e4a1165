import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Key, 
  Database,
  Edit,
  Trash2,
  Filter
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSupabase } from "@/contexts/SupabaseContext";
import { SupabaseConfig } from "@/components/SupabaseConfig";
import { useToast } from "@/hooks/use-toast";
import { usePostgrest } from "@/hooks/usePostgrest";

interface TableInfo {
  table_name: string;
  table_type: string;
  table_schema: string;
}

export default function TableEditor() {
  const [searchTerm, setSearchTerm] = useState("");
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const { select, isConfigured } = usePostgrest();
  const { toast } = useToast();

  const fetchTables = async () => {
    if (!isConfigured) return;
    
    setLoading(true);
    try {
      const data = await select('information_schema.tables', {
        select: 'table_name,table_type,table_schema',
        filter: { table_schema: 'public' }
      });

      setTables(data || []);
    } catch (error: any) {
      console.error('Error fetching tables:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar tabelas: " + error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isConfigured) {
      fetchTables();
    }
  }, [isConfigured, select]);

  const filteredTables = tables.filter(table => 
    table.table_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isConfigured) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight">Table Editor</h1>
          <p className="text-muted-foreground mt-2">
            Configure o Supabase para começar
          </p>
        </div>
        <SupabaseConfig />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Table Editor</h1>
          <p className="text-muted-foreground mt-2">
            Gerencie suas tabelas do banco de dados
          </p>
        </div>
        <Button className="bg-primary hover:bg-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Nova Tabela
        </Button>
      </div>

      <Card className="gradient-border">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Tabelas</CardTitle>
              <CardDescription>
                {loading ? "Carregando..." : `${filteredTables.length} tabelas no seu banco de dados`}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar tabelas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 w-64"
                />
              </div>
              <Button variant="outline" size="sm" onClick={fetchTables} disabled={loading}>
                <Filter className="w-4 h-4 mr-2" />
                Atualizar
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Carregando tabelas...</p>
            </div>
          ) : filteredTables.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Nenhuma tabela encontrada</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Schema</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTables.map((table) => (
                  <TableRow key={table.table_name} className="hover:bg-accent/50 transition-colors">
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <Database className="w-4 h-4 text-muted-foreground" />
                        <span>{table.table_name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={table.table_type === "BASE TABLE" ? "default" : "secondary"}>
                        {table.table_type === "BASE TABLE" ? "Tabela" : table.table_type}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-muted-foreground">{table.table_schema}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-popover border border-border">
                          <DropdownMenuItem>
                            <Edit className="w-4 h-4 mr-2" />
                            Editar Tabela
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Database className="w-4 h-4 mr-2" />
                            Ver Dados
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-destructive">
                            <Trash2 className="w-4 h-4 mr-2" />
                            Deletar Tabela
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common table operations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="outline" className="w-full justify-start">
              <Plus className="w-4 h-4 mr-2" />
              Create New Table
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Database className="w-4 h-4 mr-2" />
              Import Data
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Key className="w-4 h-4 mr-2" />
              Manage Relationships
            </Button>
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Database Stats</CardTitle>
            <CardDescription>
              Overview of your database
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Total Tables</span>
              <span className="font-semibold">{tables.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Total Rows</span>
              <span className="font-semibold">-</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Database Size</span>
              <span className="font-semibold">-</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
