
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Database, 
  Users, 
  Shield, 
  Activity, 
  ExternalLink,
  Code,
  Zap,
  Globe
} from "lucide-react";

export default function ProjectOverview() {
  const libraries = [
    { name: "JavaScript", icon: Code, color: "bg-yellow-500" },
    { name: "Flutter", icon: Zap, color: "bg-blue-500" },
    { name: "Python", icon: Globe, color: "bg-green-500" },
    { name: "<PERSON>", icon: Code, color: "bg-orange-500" },
    { name: "<PERSON><PERSON><PERSON>", icon: Code, color: "bg-purple-500" },
  ];

  const stats = [
    { title: "Database Size", value: "2.4 MB", icon: Database },
    { title: "Active Users", value: "1,247", icon: Users },
    { title: "API Requests", value: "45,231", icon: Activity },
    { title: "Storage Used", value: "156 MB", icon: Shield },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Welcome to your project</h1>
          <p className="text-muted-foreground mt-2">
            Manage your database, authentication, and more from this dashboard.
          </p>
        </div>
        <Button className="bg-primary hover:bg-primary/90">
          <ExternalLink className="w-4 h-4 mr-2" />
          View Live App
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <Card key={stat.title} className="gradient-border">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Client Libraries</CardTitle>
            <CardDescription>
              Choose your preferred programming language to get started
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {libraries.map((library) => (
              <div key={library.name} className="flex items-center justify-between p-3 rounded-lg glass-effect">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-md ${library.color} flex items-center justify-center`}>
                    <library.icon className="w-4 h-4 text-white" />
                  </div>
                  <span className="font-medium">{library.name}</span>
                  {library.name === "Kotlin" && (
                    <Badge variant="secondary" className="text-xs">Community</Badge>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm">Docs</Button>
                  <Button variant="ghost" size="sm">See GitHub</Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Example Projects</CardTitle>
            <CardDescription>
              Explore sample applications built with Supabase
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {[
                { name: "Todo App", framework: "React" },
                { name: "Blog Platform", framework: "Next.js" },
                { name: "E-commerce Store", framework: "Vue.js" },
                { name: "Social Network", framework: "Mobile Framework" },
              ].map((project) => (
                <div key={project.name} className="flex items-center justify-between p-3 rounded-lg glass-effect">
                  <div>
                    <p className="font-medium">{project.name}</p>
                    <p className="text-sm text-muted-foreground">{project.framework}</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="gradient-border">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks to help you get started with your project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <p className="font-medium">Create a table</p>
                <p className="text-sm text-muted-foreground">Start building your database schema</p>
              </div>
            </Button>
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <p className="font-medium">Setup authentication</p>
                <p className="text-sm text-muted-foreground">Enable user registration and login</p>
              </div>
            </Button>
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <p className="font-medium">Upload files</p>
                <p className="text-sm text-muted-foreground">Configure storage buckets</p>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
