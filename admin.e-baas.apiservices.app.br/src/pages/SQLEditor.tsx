import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { CodeEditor } from "@/components/CodeEditor";
import { useSupabase } from "@/contexts/SupabaseContext";
import { SupabaseConfig } from "@/components/SupabaseConfig";
import { useToast } from "@/hooks/use-toast";
import { Database, History, BookOpen } from "lucide-react";
import { usePostgrest } from "@/hooks/usePostgrest";

interface QueryResult {
  data: any[] | null;
  error: string | null;
  executionTime: number;
  rowCount: number;
}

export default function SQLEditor() {
  const [queryResult, setQueryResult] = useState<QueryResult | null>(null);
  const [queryHistory, setQueryHistory] = useState<string[]>([]);
  const { executeQuery, isConfigured } = usePostgrest();
  const { toast } = useToast();

  useEffect(() => {
    const savedHistory = localStorage.getItem('sql-query-history');
    if (savedHistory) {
      setQueryHistory(JSON.parse(savedHistory));
    }
  }, []);

  const handleExecuteQuery = async (query: string) => {
    const startTime = Date.now();
    
    try {
      const data = await executeQuery(query);
      const executionTime = Date.now() - startTime;

      const result: QueryResult = {
        data: Array.isArray(data) ? data : data ? [data] : [],
        error: null,
        executionTime,
        rowCount: Array.isArray(data) ? data.length : data ? 1 : 0
      };

      setQueryResult(result);
      
      // Add to history
      const newHistory = [query, ...queryHistory.slice(0, 9)];
      setQueryHistory(newHistory);
      localStorage.setItem('sql-query-history', JSON.stringify(newHistory));

      return result;
    } catch (error: any) {
      const result: QueryResult = {
        data: null,
        error: error.message,
        executionTime: Date.now() - startTime,
        rowCount: 0
      };
      
      setQueryResult(result);
      throw error;
    }
  };

  if (!isConfigured) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight">SQL Editor</h1>
          <p className="text-muted-foreground mt-2">
            Configure o Supabase para começar
          </p>
        </div>
        <SupabaseConfig />
      </div>
    );
  }

  const sampleQueries = [
    "SELECT * FROM information_schema.tables WHERE table_schema = 'public';",
    "SELECT schemaname, tablename, tableowner FROM pg_tables WHERE schemaname = 'public';",
    "SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = 'your_table';",
    "SHOW pg_database_size(current_database());",
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">SQL Editor</h1>
          <p className="text-muted-foreground mt-2">
            Execute queries SQL diretamente no seu banco de dados
          </p>
        </div>
        <Badge variant="default" className="flex items-center space-x-1">
          <Database className="w-3 h-3" />
          <span>PostgreSQL</span>
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3 space-y-6">
          <CodeEditor
            title="Editor SQL"
            language="sql"
            onExecute={handleExecuteQuery}
            height="300px"
            initialCode="-- Digite sua query SQL aqui
SELECT * FROM information_schema.tables 
WHERE table_schema = 'public'
LIMIT 10;"
          />

          {queryResult && (
            <Card className="gradient-border">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Resultado da Query</CardTitle>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span>{queryResult.rowCount} linhas</span>
                    <span>{queryResult.executionTime}ms</span>
                    {queryResult.error ? (
                      <Badge variant="destructive">Erro</Badge>
                    ) : (
                      <Badge variant="default">Sucesso</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {queryResult.error ? (
                  <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                    <p className="text-destructive font-mono text-sm">{queryResult.error}</p>
                  </div>
                ) : queryResult.data && queryResult.data.length > 0 ? (
                  <div className="overflow-auto max-h-96">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {Object.keys(queryResult.data[0]).map((column) => (
                            <TableHead key={column}>{column}</TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {queryResult.data.map((row, index) => (
                          <TableRow key={index}>
                            {Object.values(row).map((value: any, cellIndex) => (
                              <TableCell key={cellIndex} className="font-mono text-sm">
                                {value === null ? (
                                  <span className="text-muted-foreground italic">NULL</span>
                                ) : typeof value === 'object' ? (
                                  JSON.stringify(value)
                                ) : (
                                  String(value)
                                )}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-8">
                    Query executada com sucesso - Nenhum resultado retornado
                  </p>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          <Card className="gradient-border">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BookOpen className="w-5 h-5" />
                <span>Queries de Exemplo</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {sampleQueries.map((query, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="w-full text-left h-auto p-3 whitespace-normal font-mono text-xs"
                  onClick={() => {
                    const editor = document.querySelector('textarea') as HTMLTextAreaElement;
                    if (editor) {
                      editor.value = query;
                      editor.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                  }}
                >
                  {query}
                </Button>
              ))}
            </CardContent>
          </Card>

          {queryHistory.length > 0 && (
            <Card className="gradient-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <History className="w-5 h-5" />
                  <span>Histórico</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {queryHistory.slice(0, 5).map((query, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    className="w-full text-left h-auto p-3 whitespace-normal font-mono text-xs"
                    onClick={() => {
                      const editor = document.querySelector('textarea') as HTMLTextAreaElement;
                      if (editor) {
                        editor.value = query;
                        editor.dispatchEvent(new Event('input', { bubbles: true }));
                      }
                    }}
                  >
                    {query.substring(0, 100)}...
                  </Button>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
