
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "./AppSidebar";
import { Button } from "@/components/ui/button";
import { Wifi, AlertTriangle, WifiOff } from "lucide-react";
import { useSupabase } from "@/contexts/SupabaseContext";

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const { isConfigured } = useSupabase();

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <main className="flex-1 flex flex-col">
          <header className="border-b border-border bg-card px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <SidebarTrigger className="text-muted-foreground hover:text-foreground" />
                <div className="flex items-center space-x-2 text-sm">
                  <span className="text-foreground font-medium">Supabase Manager</span>
                  <div className={`flex items-center space-x-1 ${isConfigured ? 'text-green-400' : 'text-red-400'}`}>
                    {isConfigured ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                    <span className="text-xs">{isConfigured ? 'Conectado' : 'Desconectado'}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Button variant="outline" size="sm" className="text-xs">
                  <AlertTriangle className="w-3 h-3 mr-1 text-yellow-500" />
                  Security Issues
                </Button>
                <Button variant="outline" size="sm" className="text-xs">
                  Docs
                </Button>
                <Button variant="outline" size="sm" className="text-xs">
                  Ver GitHub
                </Button>
              </div>
            </div>
          </header>
          
          <div className="flex-1 p-6 animate-fade-in">
            {children}
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
}
