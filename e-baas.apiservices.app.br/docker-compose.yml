version: "3.8"

services:
  app:
    build: .
    container_name: ebaas_app
    ports:
      - "3333:3333"
    volumes:
      - ./storage:/usr/src/app/storage
      - ./logs:/usr/src/app/logs
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/ebaas_main
      - MYSQL_URL=mysql://mysql:mysql@mysql:3306/ebaas_main
      - MONGODB_URL=**************************************************
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - STORAGE_PATH=/usr/src/app/storage
      - BASE_URL=http://localhost:3333
    depends_on:
      - postgres
      - mysql
      - mongodb
      - redis
    networks:
      - ebaas-network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:3333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:14-alpine
    container_name: ebaas_postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: ebaas_main
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ebaas-network
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    container_name: ebaas_mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: mysql
      MYSQL_PASSWORD: mysql
      MYSQL_DATABASE: ebaas_main
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - ebaas-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  mongodb:
    image: mongo:latest
    container_name: ebaas_mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: mongodb
      MONGO_INITDB_ROOT_PASSWORD: mongodb
      MONGO_INITDB_DATABASE: ebaas_main
    volumes:
      - mongodb_data:/data/db
    networks:
      - ebaas-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: ebaas_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ebaas-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  # Nginx reverse proxy (opcional para produção)
  nginx:
    image: nginx:alpine
    container_name: ebaas_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - ebaas-network
    restart: unless-stopped
    profiles:
      - production

networks:
  ebaas-network:
    driver: bridge

volumes:
  postgres_data:
  mysql_data:
  mongodb_data:
  redis_data:
