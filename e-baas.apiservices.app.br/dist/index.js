"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cors_1 = __importDefault(require("cors"));
const data_source_1 = require("data-source");
const dotenv_1 = __importDefault(require("dotenv"));
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const user_controller_1 = __importDefault(require("modules/users/user.controller"));
const auth_controller_1 = __importDefault(require("modules/auth/auth.controller"));
const postgrest_controller_1 = __importDefault(require("modules/postgrest/postgrest.controller"));
const sqlExecutor_controller_1 = __importDefault(require("modules/sqlExecutor/sqlExecutor.controller"));
const rlsPolicies_1 = __importDefault(require("modules/rlsPolicies"));
const api_key_controller_1 = __importDefault(require("modules/api-keys/api-key.controller"));
const database_management_controller_1 = __importDefault(require("modules/database-management/database-management.controller"));
const storage_controller_1 = __importDefault(require("modules/storage/storage.controller"));
const realtime_controller_1 = __importDefault(require("modules/realtime/realtime.controller"));
const queues_1 = __importDefault(require("modules/queues"));
const mcp_1 = __importDefault(require("modules/mcp"));
const edge_functions_1 = __importDefault(require("modules/edge-functions"));
const admin_controller_1 = __importDefault(require("modules/admin/admin.controller"));
const websocket_server_1 = __importDefault(require("modules/realtime/websocket.server"));
const queues_useCases_1 = require("modules/queues/queues.useCases");
dotenv_1.default.config();
const app = (0, express_1.default)();
app.use(express_1.default.json());
app.use((0, cors_1.default)({
    origin: "*",
}));
// Auth routes
app.use("/auth/v1", auth_controller_1.default);
// API routes
app.use("/api/users", user_controller_1.default);
app.use("/rest/v1", postgrest_controller_1.default);
app.use("/sql/v1", sqlExecutor_controller_1.default);
app.use("/rls/v1", rlsPolicies_1.default);
app.use("/api-keys/v1", api_key_controller_1.default);
app.use("/database/v1", database_management_controller_1.default);
app.use("/storage/v1", storage_controller_1.default);
app.use("/realtime/v1", realtime_controller_1.default);
app.use("/queues/v1", queues_1.default);
app.use("/mcp/v1", mcp_1.default);
app.use("/functions/v1", edge_functions_1.default);
app.use("/admin/v1", admin_controller_1.default);
app.use("/", (req, res) => {
    res.json({ message: "Api Status Ok" });
});
// Create HTTP server
const httpServer = (0, http_1.createServer)(app);
// Initialize WebSocket server and Queue consumers
let webSocketServer;
let queuesUseCases;
data_source_1.AppDataSource.initialize()
    .then(async () => {
    console.log("Data source has been initialized!");
    // Initialize WebSocket server after database connection
    webSocketServer = new websocket_server_1.default(httpServer);
    console.log("WebSocket server initialized!");
    // Initialize queue consumers
    queuesUseCases = new queues_useCases_1.QueuesUseCases();
    await queuesUseCases.setupDefaultConsumers();
    console.log("Queue consumers initialized!");
    const PORT = process.env.PORT || 3333;
    httpServer.listen(PORT, () => {
        console.log(`Server running on port ${PORT}`);
        console.log(`WebSocket endpoint: ws://localhost:${PORT}/realtime/socket.io`);
        console.log(`Queue API endpoint: http://localhost:${PORT}/queues/v1`);
        console.log(`MCP API endpoint: http://localhost:${PORT}/mcp/v1`);
    });
})
    .catch((error) => console.log(error));
exports.default = app;
//# sourceMappingURL=index.js.map