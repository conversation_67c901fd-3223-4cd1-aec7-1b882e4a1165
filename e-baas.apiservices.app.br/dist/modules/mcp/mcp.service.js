"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.McpService = void 0;
const sqlExecutor_useCases_1 = require("../sqlExecutor/sqlExecutor.useCases");
const postgrest_useCases_1 = require("../postgrest/postgrest.useCases");
const queues_useCases_1 = require("../queues/queues.useCases");
const auth_useCases_1 = require("../auth/auth.useCases");
const user_useCases_1 = require("../users/user.useCases");
const workspace_useCases_1 = require("../workspaces/workspace.useCases");
class McpService {
    constructor() {
        this.sqlExecutor = new sqlExecutor_useCases_1.SqlExecutorUseCases();
        this.postgrest = new postgrest_useCases_1.PostgrestUseCases();
        this.queues = new queues_useCases_1.QueuesUseCases();
        this.auth = new auth_useCases_1.AuthUseCases();
        this.users = new user_useCases_1.UserUseCases();
        this.workspaces = new workspace_useCases_1.WorkspaceUseCases();
    }
    async executeCustomCommand(command, args, context) {
        throw new Error(`Custom command '${command}' not implemented`);
    }
    async getDatabaseSchema(workspaceId) {
        try {
            // Usar PostgrestUseCases para obter schema das tabelas
            return await this.postgrest.getTableStructure();
        }
        catch (error) {
            console.error(`❌ Failed to get database schema for workspace ${workspaceId}:`, error);
            throw error;
        }
    }
    async executeQuery(workspaceId, query, params) {
        try {
            // Usar SqlExecutorUseCases para executar queries
            const result = await this.sqlExecutor.executeQuery({
                workspaceId,
                query,
                parameters: params || []
            });
            return result;
        }
        catch (error) {
            console.error(`❌ Failed to execute query in workspace ${workspaceId}:`, error);
            throw error;
        }
    }
    async manageTable(workspaceId, action, tableName, schema) {
        try {
            switch (action) {
                case 'create':
                    if (!schema) {
                        throw new Error('Schema is required for table creation');
                    }
                    // Construir CREATE TABLE statement baseado no schema
                    const createQuery = this.buildCreateTableQuery(tableName, schema);
                    return await this.executeQuery(workspaceId, createQuery);
                case 'drop':
                    const dropQuery = `DROP TABLE IF EXISTS ${tableName}`;
                    return await this.executeQuery(workspaceId, dropQuery);
                case 'describe':
                    const describeQuery = `SELECT column_name, data_type, is_nullable, column_default 
                                FROM information_schema.columns 
                                WHERE table_name = '${tableName}'`;
                    return await this.executeQuery(workspaceId, describeQuery);
                case 'list':
                    const listQuery = `SELECT table_name FROM information_schema.tables 
                            WHERE table_schema = 'public'`;
                    return await this.executeQuery(workspaceId, listQuery);
                default:
                    throw new Error(`Unsupported table operation: ${action}`);
            }
        }
        catch (error) {
            console.error(`❌ Failed to ${action} table ${tableName}:`, error);
            throw error;
        }
    }
    async manageStorage(workspaceId, action, options) {
        try {
            // Por enquanto, retornar mock até implementar storage completo
            switch (action) {
                case 'upload':
                    return {
                        success: true,
                        bucket: options.bucketName,
                        fileName: options.fileName,
                        url: `/storage/v1/${options.bucketName}/${options.fileName}`,
                        message: 'File uploaded successfully (mock)'
                    };
                case 'download':
                    return {
                        success: true,
                        bucket: options.bucketName,
                        fileName: options.fileName,
                        url: `/storage/v1/${options.bucketName}/${options.fileName}`,
                        message: 'File download URL generated (mock)'
                    };
                case 'delete':
                    return {
                        success: true,
                        bucket: options.bucketName,
                        fileName: options.fileName,
                        message: 'File deleted successfully (mock)'
                    };
                case 'list':
                    return {
                        success: true,
                        bucket: options.bucketName,
                        files: [
                            'example1.jpg',
                            'example2.png',
                            'document.pdf'
                        ],
                        message: 'Files listed successfully (mock)'
                    };
                default:
                    throw new Error(`Unsupported storage operation: ${action}`);
            }
        }
        catch (error) {
            console.error(`❌ Failed to ${action} storage:`, error);
            throw error;
        }
    }
    async manageAuth(workspaceId, action, options) {
        try {
            switch (action) {
                case 'create_user':
                    if (!options.userData?.email || !options.userData?.password) {
                        throw new Error('Email and password are required for user creation');
                    }
                    return await this.users.createUser({
                        email: options.userData.email,
                        password: options.userData.password,
                        workspaceId: workspaceId
                    });
                case 'delete_user':
                    if (!options.userId) {
                        throw new Error('User ID is required for user deletion');
                    }
                    return await this.users.deleteUser(options.userId);
                case 'get_user':
                    if (!options.userId) {
                        throw new Error('User ID is required');
                    }
                    return await this.users.getUserById(options.userId);
                case 'list_users':
                    return await this.users.getAllUsers();
                case 'update_permissions':
                    return {
                        success: true,
                        userId: options.userId,
                        permissions: options.permissions,
                        message: 'Permissions updated successfully (mock)'
                    };
                default:
                    throw new Error(`Unsupported auth operation: ${action}`);
            }
        }
        catch (error) {
            console.error(`❌ Failed to ${action} auth:`, error);
            throw error;
        }
    }
    async publishEvent(workspaceId, eventType, data, options) {
        try {
            // Adicionar contexto do workspace aos dados do evento
            const eventData = {
                ...data,
                workspaceId,
                timestamp: new Date().toISOString()
            };
            return await this.queues.publishMessage(eventType, eventData, options);
        }
        catch (error) {
            console.error(`❌ Failed to publish event ${eventType}:`, error);
            throw error;
        }
    }
    async getSystemInfo() {
        try {
            return {
                version: '1.0.0',
                name: 'E-BaaS MCP Server',
                description: 'Backend as a Service with MCP integration for LLMs',
                features: [
                    'Database Management',
                    'SQL Execution',
                    'Authentication',
                    'Storage (S3-compatible)',
                    'Real-time subscriptions',
                    'Queue Management',
                    'Row-Level Security (RLS)',
                    'Edge Functions (planned)'
                ],
                endpoints: {
                    database: '/sql/v1',
                    auth: '/auth/v1',
                    storage: '/storage/v1',
                    realtime: '/realtime/v1',
                    queues: '/queues/v1',
                    mcp: '/mcp/v1'
                },
                environment: process.env.NODE_ENV || 'development',
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Failed to get system info:', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            const checks = {
                database: false,
                queues: false,
                storage: false,
                auth: false
            };
            // Verificar conexão com banco de dados
            try {
                await this.executeQuery('default', 'SELECT 1');
                checks.database = true;
            }
            catch (error) {
                console.warn('Database health check failed:', error);
            }
            // Verificar sistema de filas
            try {
                await this.queues.getQueueStatus();
                checks.queues = true;
            }
            catch (error) {
                console.warn('Queues health check failed:', error);
            }
            // Storage e Auth serão sempre true por enquanto (mock)
            checks.storage = true;
            checks.auth = true;
            const isHealthy = Object.values(checks).every(status => status);
            return {
                status: isHealthy ? 'healthy' : 'degraded',
                checks,
                timestamp: new Date().toISOString(),
                uptime: process.uptime()
            };
        }
        catch (error) {
            console.error('❌ Health check failed:', error);
            return {
                status: 'unhealthy',
                checks: {
                    database: false,
                    queues: false,
                    storage: false,
                    auth: false
                },
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    buildCreateTableQuery(tableName, schema) {
        const columns = [];
        for (const [columnName, columnType] of Object.entries(schema)) {
            columns.push(`${columnName} ${this.mapDataType(columnType)}`);
        }
        return `CREATE TABLE IF NOT EXISTS ${tableName} (
      id SERIAL PRIMARY KEY,
      ${columns.join(',\n      ')},
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`;
    }
    mapDataType(type) {
        const typeMap = {
            'string': 'VARCHAR(255)',
            'text': 'TEXT',
            'number': 'INTEGER',
            'float': 'DECIMAL',
            'boolean': 'BOOLEAN',
            'date': 'TIMESTAMP',
            'json': 'JSONB'
        };
        return typeMap[type.toLowerCase()] || 'VARCHAR(255)';
    }
}
exports.McpService = McpService;
//# sourceMappingURL=mcp.service.js.map