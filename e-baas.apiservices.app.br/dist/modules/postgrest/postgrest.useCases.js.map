{"version": 3, "file": "postgrest.useCases.js", "sourceRoot": "", "sources": ["../../../src/modules/postgrest/postgrest.useCases.ts"], "names": [], "mappings": ";;AACA,iDAAuD;AAEvD,uDAAmD;AAEnD,MAAqB,iBAAiB;IAClC,gBAAe,CAAC;IAEhB,KAAK,CAAC,MAAM;QACR,IAAI;YACA,OAAO,MAAM,gCAAmB,CAAC,IAAI,EAAE,CAAC;SAC3C;QAAC,OAAO,KAAc,EAAE;YACrB,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SACjD;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACnB,IAAI;YACA,MAAM,SAAS,GAAG,MAAM,gCAAmB,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9D,IAAI,CAAC,SAAS,EAAE;gBACZ,MAAM,4BAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;aACtD;YACD,OAAO,SAAS,CAAC;SACpB;QAAC,OAAO,KAAc,EAAE;YACrB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;gBACnE,MAAM,KAAK,CAAC;aACf;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SACjD;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAAwB;QACjC,IAAI;YACA,OAAO,MAAM,gCAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC/C;QAAC,OAAO,KAAc,EAAE;YACrB,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SACjD;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,IAAwB;QAC7C,IAAI;YACA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACxC,MAAM,gCAAmB,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YAC3C,OAAO,EAAE,GAAG,SAAS,EAAE,GAAG,IAAI,EAAE,CAAC;SACpC;QAAC,OAAO,KAAc,EAAE;YACrB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;gBACnE,MAAM,KAAK,CAAC;aACf;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SACjD;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACnB,IAAI;YACA,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACtB,MAAM,gCAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACxC;QAAC,OAAO,KAAc,EAAE;YACrB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;gBACnE,MAAM,KAAK,CAAC;aACf;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SACjD;IACL,CAAC;CACJ;AA1DD,oCA0DC"}