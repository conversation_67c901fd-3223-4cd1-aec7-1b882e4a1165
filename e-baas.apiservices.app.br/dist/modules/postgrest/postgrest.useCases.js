"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const repository_1 = require("infra/repository");
const errorHandlers_1 = require("infra/errorHandlers");
class PostgrestUseCases {
    constructor() { }
    async getAll() {
        try {
            return await repository_1.postgrestRepository.find();
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async getOne(id) {
        try {
            const postgrest = await repository_1.postgrestRepository.findOneBy({ id });
            if (!postgrest) {
                throw errorHandlers_1.ErrorHandler.NotFound("Postgrest not found");
            }
            return postgrest;
        }
        catch (error) {
            if (error instanceof Error && error.message === "Postgrest not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async create(data) {
        try {
            return await repository_1.postgrestRepository.save(data);
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async update(id, data) {
        try {
            const postgrest = await this.getOne(id);
            await repository_1.postgrestRepository.update(id, data);
            return { ...postgrest, ...data };
        }
        catch (error) {
            if (error instanceof Error && error.message === "Postgrest not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async delete(id) {
        try {
            await this.getOne(id);
            await repository_1.postgrestRepository.delete(id);
        }
        catch (error) {
            if (error instanceof Error && error.message === "Postgrest not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
}
exports.default = PostgrestUseCases;
//# sourceMappingURL=postgrest.useCases.js.map