"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const repository_1 = require("../../infra/repository");
const errorHandlers_1 = require("../../infra/errorHandlers");
const config_1 = __importDefault(require("../../config"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
class AuthUseCases {
    constructor() { }
    async signUp(data) {
        try {
            // Verificar se o usuário já existe
            const existingUser = await repository_1.userRepository.findOne({
                where: { email: data.email },
            });
            if (existingUser) {
                throw errorHandlers_1.ErrorHandler.BadRequest("Email already exists");
            }
            // Criar novo usuário
            const user = repository_1.userRepository.create({
                firstName: data.firstName,
                lastName: data.lastName,
                email: data.email,
                password: data.password,
            });
            await repository_1.userRepository.save(user);
            // Gerar tokens
            const { accessToken, refreshToken } = await this.generateTokens(user);
            return { user, accessToken, refreshToken };
        }
        catch (error) {
            if (error instanceof Error && error.message === "Email already exists") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async signIn(data) {
        try {
            // Buscar usuário com a senha (que normalmente está excluída nas consultas)
            const user = await repository_1.userRepository.findOne({
                where: { email: data.email },
                select: {
                    id: true,
                    email: true,
                    password: true,
                    firstName: true,
                    lastName: true,
                    isActive: true,
                    createdAt: true,
                    updatedAt: true,
                },
            });
            if (!user) {
                throw errorHandlers_1.ErrorHandler.Unauthorized("Invalid credentials");
            }
            // Verificar senha
            const isPasswordValid = await user.comparePassword(data.password);
            if (!isPasswordValid) {
                throw errorHandlers_1.ErrorHandler.Unauthorized("Invalid credentials");
            }
            // Verificar se o usuário está ativo
            if (!user.isActive) {
                throw errorHandlers_1.ErrorHandler.Unauthorized("User account is disabled");
            }
            // Gerar tokens
            const { accessToken, refreshToken } = await this.generateTokens(user);
            return { user, accessToken, refreshToken };
        }
        catch (error) {
            if (error instanceof Error &&
                (error.message === "Invalid credentials" ||
                    error.message === "User account is disabled")) {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async refreshToken(token) {
        try {
            // Buscar o refresh token no banco
            const refreshTokenEntity = await repository_1.refreshTokenRepository.findOne({
                where: { token },
                relations: ["user"],
            });
            if (!refreshTokenEntity) {
                throw errorHandlers_1.ErrorHandler.Unauthorized("Invalid refresh token");
            }
            // Verificar se o token está expirado ou revogado
            if (refreshTokenEntity.isExpired() || refreshTokenEntity.isRevoked) {
                throw errorHandlers_1.ErrorHandler.Unauthorized("Refresh token expired");
            }
            // Revogar o token atual
            refreshTokenEntity.isRevoked = true;
            await repository_1.refreshTokenRepository.save(refreshTokenEntity);
            // Gerar novos tokens
            const { accessToken, refreshToken } = await this.generateTokens(refreshTokenEntity.user);
            return { accessToken, refreshToken };
        }
        catch (error) {
            if (error instanceof Error &&
                (error.message === "Invalid refresh token" ||
                    error.message === "Refresh token expired")) {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async generateTokens(user) {
        // Gerar access token
        const accessToken = jsonwebtoken_1.default.sign({ sub: user.id, email: user.email }, config_1.default.jwt.secret, { expiresIn: config_1.default.jwt.expiresIn });
        // Criar refresh token
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 7); // Expira em 7 dias
        const refreshTokenEntity = repository_1.refreshTokenRepository.create({
            userId: user.id,
            expiresAt: expirationDate,
        });
        await repository_1.refreshTokenRepository.save(refreshTokenEntity);
        return {
            accessToken,
            refreshToken: refreshTokenEntity.token,
        };
    }
}
exports.default = AuthUseCases;
//# sourceMappingURL=auth.useCases.js.map