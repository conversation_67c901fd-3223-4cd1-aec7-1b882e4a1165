{
  "compilerOptions": {
    // "lib": ["es5", "es6"],
    "target": "ES2021",
    "module": "commonjs",
    "outDir": "./build",
    "experimentalDecorators": true,
    "sourceMap": true,
    "types": ["jest", "node", "express"],
    "typeRoots": ["./node_modules/@types", "./src/@types"],
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": false,
    "emitDecoratorMetadata": true,
    "baseUrl": ".",
    "paths": {
      "*": ["*"],
      "src/*": ["src/*"],
      "infra": ["src/infra"],
      "infra/*": ["src/infra/*"],
      "infra/config": ["src/infra/config"],
      "modules/*": ["src/modules/*"],
      "@types/*": ["src/@types/*"],
      "data-source": ["src/infra/database/data-source"]
    },
    "resolveJsonModule": true
  }
}
