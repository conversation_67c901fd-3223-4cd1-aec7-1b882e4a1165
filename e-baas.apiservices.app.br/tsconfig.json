{
  "compilerOptions": {
    // "lib": ["es5", "es6"],
    "target": "ES2021",
    "module": "commonjs",
    "outDir": "./dist",
    "experimentalDecorators": true,
    "sourceMap": true,
    "types": ["jest", "node", "express"],
    "typeRoots": ["./node_modules/@types", "./src/@types"],
    "rootDir": "./src",
    "strict": false,
    "strictPropertyInitialization": false,
    "noImplicitAny": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": false,
    "emitDecoratorMetadata": true,
    "baseUrl": ".",
    "paths": {
      "*": ["*"],
      "src/*": ["src/*"],
      "infra": ["src/infra"],
      "infra/*": ["src/infra/*"],
      "infra/config": ["src/infra/config"],
      "modules/*": ["src/modules/*"],
      "@types/*": ["src/@types/*"],
      "data-source": ["src/infra/database/data-source"]
    },
    "resolveJsonModule": true
  },
  "include": ["src/**/*"],
  "exclude": [
    "node_modules",
    "build",
    "dist",
    "sdk",
    "admin.e-baas.apiservices.app.br",
    "docs",
    "tests",
    "**/*.test.ts",
    "**/*.spec.ts"
  ]
}
