// Main E-BaaS SDK exports
export { EBaaSClient, createClient } from './EBaaSClient';
export type { EBaaSClientOptions } from './EBaaSClient';

// Auth module
export { AuthClient } from './auth/AuthClient';
export type { 
  User, 
  Session, 
  AuthResponse, 
  SignUpData, 
  SignInData,
  AuthChangeEvent,
  AuthEventType 
} from './auth/types';

// Database module  
export { DatabaseClient } from './database/DatabaseClient';
export { QueryBuilder } from './database/QueryBuilder';
export type { 
  QueryResponse, 
  InsertResponse, 
  UpdateResponse, 
  DeleteResponse,
  DatabaseFilter,
  DatabaseOrder
} from './database/types';

// Storage module
export { StorageClient } from './storage/StorageClient';
export { StorageBucket } from './storage/StorageBucket';
export type { 
  FileObject, 
  UploadResponse, 
  SignedUrlResponse, 
  DownloadResponse,
  BucketOptions,
  UploadOptions 
} from './storage/types';

// Realtime module
export { RealtimeClient } from './realtime/RealtimeClient';
export { RealtimeChannel } from './realtime/RealtimeChannel';
export type { 
  RealtimeMessage, 
  ChannelSubscription, 
  PresenceState,
  RealtimeEvent
} from './realtime/types';

// Edge Functions module
export { EdgeFunctionsClient } from './edge-functions/EdgeFunctionsClient';
export type { 
  FunctionInvokeOptions, 
  FunctionResponse,
  EdgeFunctionMeta 
} from './edge-functions/types';

// Common types and utilities
export type { EBaaSResponse } from './lib/types';
export { EBaaSError } from './lib/EBaaSError';