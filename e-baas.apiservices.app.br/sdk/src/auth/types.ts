export interface User {
  id: string;
  email: string;
  name?: string;
  picture?: string;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  lastSignInAt?: string;
  metadata?: Record<string, any>;
  appMetadata?: Record<string, any>;
}

export interface Session {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  expires_at: number;
  token_type: string;
  user: User;
}

export interface AuthResponse {
  data: {
    user: User | null;
    session: Session | null;
  };
  error: Error | null;
}

export interface SignUpData {
  email: string;
  password: string;
  name?: string;
  metadata?: Record<string, any>;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface OAuthSignInData {
  provider: 'google' | 'github' | 'facebook';
  redirectTo?: string;
  scopes?: string;
}

export interface ResetPasswordData {
  email: string;
  redirectTo?: string;
}

export interface UpdateUserData {
  email?: string;
  password?: string;
  name?: string;
  metadata?: Record<string, any>;
}

export type AuthEventType = 
  | 'SIGNED_IN' 
  | 'SIGNED_OUT' 
  | 'TOKEN_REFRESHED' 
  | 'USER_UPDATED'
  | 'PASSWORD_RECOVERY';

export interface AuthChangeEvent {
  event: AuthEventType;
  session: Session | null;
}

export type AuthChangeCallback = (event: AuthEventType, session: Session | null) => void;

export interface AuthClientOptions {
  autoRefreshToken?: boolean;
  persistSession?: boolean;
  detectSessionInUrl?: boolean;
  flowType?: 'implicit' | 'pkce';
  debug?: boolean;
}