export class EBaaSError extends <PERSON>rror {
  public readonly status: number;
  public readonly details?: any;

  constructor(message: string, status: number = 0, details?: any) {
    super(message);
    this.name = 'EBaaSError';
    this.status = status;
    this.details = details;

    // Maintain proper stack trace for where error was thrown (V8 only)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, EBaaSError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      status: this.status,
      details: this.details,
      stack: this.stack
    };
  }

  override toString() {
    return `${this.name}: ${this.message} (Status: ${this.status})`;
  }
}