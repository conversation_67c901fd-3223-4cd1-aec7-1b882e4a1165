import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { EBaaSError } from './EBaaSError';
import { EBaaSResponse } from './types';

export interface HttpClientOptions {
  baseURL: string;
  headers?: Record<string, string>;
  timeout?: number;
}

export class HttpClient {
  private client: AxiosInstance;

  constructor(options: HttpClientOptions) {
    this.client = axios.create({
      baseURL: options.baseURL,
      timeout: options.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add any global request modifications here
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (error.response) {
          // Server responded with error status
          const ebaaError = new EBaaSError(
            error.response.data?.error || error.response.data?.message || error.message,
            error.response.status,
            error.response.data
          );
          return Promise.reject(ebaaError);
        } else if (error.request) {
          // Request was made but no response received
          const ebaaError = new EBaaSError(
            'Network error - no response received',
            0,
            { originalError: error }
          );
          return Promise.reject(ebaaError);
        } else {
          // Something else happened
          const ebaaError = new EBaaSError(
            error.message || 'Unknown error occurred',
            0,
            { originalError: error }
          );
          return Promise.reject(ebaaError);
        }
      }
    );
  }

  setAuthToken(token: string) {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  removeAuthToken() {
    delete this.client.defaults.headers.common['Authorization'];
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.get(url, config);
      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        error: null
      };
    } catch (error) {
      if (error instanceof EBaaSError) {
        return {
          data: null,
          status: error.status,
          statusText: error.message,
          error
        };
      }
      throw error;
    }
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.post(url, data, config);
      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        error: null
      };
    } catch (error) {
      if (error instanceof EBaaSError) {
        return {
          data: null,
          status: error.status,
          statusText: error.message,
          error
        };
      }
      throw error;
    }
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.put(url, data, config);
      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        error: null
      };
    } catch (error) {
      if (error instanceof EBaaSError) {
        return {
          data: null,
          status: error.status,
          statusText: error.message,
          error
        };
      }
      throw error;
    }
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.patch(url, data, config);
      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        error: null
      };
    } catch (error) {
      if (error instanceof EBaaSError) {
        return {
          data: null,
          status: error.status,
          statusText: error.message,
          error
        };
      }
      throw error;
    }
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.delete(url, config);
      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        error: null
      };
    } catch (error) {
      if (error instanceof EBaaSError) {
        return {
          data: null,
          status: error.status,
          statusText: error.message,
          error
        };
      }
      throw error;
    }
  }

  // Raw axios instance for advanced usage
  get axios() {
    return this.client;
  }
}