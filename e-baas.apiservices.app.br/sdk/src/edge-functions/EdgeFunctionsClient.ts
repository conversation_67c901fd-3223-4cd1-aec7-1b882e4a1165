import { HttpClient } from '../lib/HttpClient';
import { EBaaSError } from '../lib/EBaaSError';
import { 
  FunctionInvokeOptions, 
  FunctionResponse, 
  EdgeFunctionMeta,
  EdgeFunctionsClientOptions 
} from './types';

export class EdgeFunctionsClient {
  private httpClient: HttpClient;
  private options: EdgeFunctionsClientOptions;

  constructor(httpClient: HttpClient, options: EdgeFunctionsClientOptions) {
    this.httpClient = httpClient;
    this.options = options;
  }

  /**
   * Invoke an edge function
   */
  async invoke<T = any>(
    functionName: string, 
    options?: FunctionInvokeOptions
  ): Promise<FunctionResponse<T>> {
    try {
      const method = options?.method || 'POST';
      const headers = {
        'Content-Type': 'application/json',
        'X-Workspace-Id': this.options.workspaceId,
        ...options?.headers
      };

      const config = {
        headers,
        ...(options?.body && { data: options.body })
      };

      let response;
      switch (method) {
        case 'GET':
          response = await this.httpClient.get<T>(`/api/v1/functions/${functionName}`, config);
          break;
        case 'POST':
          response = await this.httpClient.post<T>(`/api/v1/functions/${functionName}`, options?.body, config);
          break;
        case 'PUT':
          response = await this.httpClient.put<T>(`/api/v1/functions/${functionName}`, options?.body, config);
          break;
        case 'PATCH':
          response = await this.httpClient.patch<T>(`/api/v1/functions/${functionName}`, options?.body, config);
          break;
        case 'DELETE':
          response = await this.httpClient.delete<T>(`/api/v1/functions/${functionName}`, config);
          break;
        default:
          throw new EBaaSError(`Unsupported HTTP method: ${method}`, 400);
      }

      if (response.error) {
        return {
          data: null,
          error: response.error,
          status: response.status,
          statusText: response.statusText
        };
      }

      return {
        data: response.data,
        error: null,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      const functionError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Function invocation failed',
        0,
        error
      );

      return {
        data: null,
        error: functionError,
        status: functionError.status,
        statusText: functionError.message
      };
    }
  }

  /**
   * List available edge functions
   */
  async list(): Promise<{ data: EdgeFunctionMeta[] | null; error: Error | null }> {
    try {
      const response = await this.httpClient.get<EdgeFunctionMeta[]>('/api/v1/functions', {
        params: { workspaceId: this.options.workspaceId }
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data || [],
        error: null
      };
    } catch (error) {
      const functionError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Failed to list functions',
        0,
        error
      );

      return {
        data: null,
        error: functionError
      };
    }
  }

  /**
   * Get edge function metadata
   */
  async get(functionName: string): Promise<{ data: EdgeFunctionMeta | null; error: Error | null }> {
    try {
      const response = await this.httpClient.get<EdgeFunctionMeta>(`/api/v1/functions/${functionName}/meta`, {
        params: { workspaceId: this.options.workspaceId }
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      const functionError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Failed to get function metadata',
        0,
        error
      );

      return {
        data: null,
        error: functionError
      };
    }
  }

  /**
   * Deploy an edge function
   */
  async deploy(
    functionName: string, 
    code: string,
    options?: {
      description?: string;
      runtime?: string;
      env?: Record<string, string>;
    }
  ): Promise<{ data: EdgeFunctionMeta | null; error: Error | null }> {
    try {
      const response = await this.httpClient.post<EdgeFunctionMeta>('/api/v1/functions', {
        name: functionName,
        code,
        workspaceId: this.options.workspaceId,
        description: options?.description,
        runtime: options?.runtime || 'deno',
        env: options?.env
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      const functionError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Function deployment failed',
        0,
        error
      );

      return {
        data: null,
        error: functionError
      };
    }
  }

  /**
   * Delete an edge function
   */
  async delete(functionName: string): Promise<{ data: { message: string } | null; error: Error | null }> {
    try {
      const response = await this.httpClient.delete(`/api/v1/functions/${functionName}`, {
        params: { workspaceId: this.options.workspaceId }
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data || { message: 'Function deleted successfully' },
        error: null
      };
    } catch (error) {
      const functionError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Function deletion failed',
        0,
        error
      );

      return {
        data: null,
        error: functionError
      };
    }
  }
}