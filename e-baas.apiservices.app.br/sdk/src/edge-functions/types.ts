export interface FunctionInvokeOptions {
  headers?: Record<string, string>;
  body?: any;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
}

export interface FunctionResponse<T = any> {
  data: T | null;
  error: Error | null;
  status: number;
  statusText: string;
}

export interface EdgeFunctionMeta {
  id: string;
  name: string;
  description?: string;
  version: string;
  runtime: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface EdgeFunctionsClientOptions {
  workspaceId: string;
}