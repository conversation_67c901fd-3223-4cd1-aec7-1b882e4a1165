import { HttpClient } from '../lib/HttpClient';
import { QueryBuilder } from './QueryBuilder';
import { RpcResponse, DatabaseOptions } from './types';
import { EBaaSError } from '../lib/EBaaSError';

export class DatabaseClient {
  private httpClient: HttpClient;
  private options: DatabaseOptions;

  constructor(httpClient: HttpClient, options: DatabaseOptions) {
    this.httpClient = httpClient;
    this.options = options;
  }

  /**
   * Create a query builder for a table
   * Usage: db.from('users').select('*').eq('id', 1)
   */
  from<T = any>(table: string): QueryBuilder<T> {
    return new QueryBuilder<T>(
      this.httpClient, 
      table, 
      this.options.workspaceId,
      this.options.schema
    );
  }

  /**
   * Execute a remote procedure call (RPC)
   * Usage: db.rpc('my_function', { param1: 'value' })
   */
  async rpc<T = any>(functionName: string, params?: Record<string, any>): Promise<RpcResponse<T>> {
    try {
      const endpoint = `/api/v1/database/rpc/${functionName}`;
      const response = await this.httpClient.post<T>(endpoint, {
        ...params,
        workspaceId: this.options.workspaceId,
        schema: this.options.schema
      });

      if (response.error) {
        return {
          data: null,
          error: response.error,
          status: response.status,
          statusText: response.statusText
        };
      }

      return {
        data: response.data,
        error: null,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      const ebaaError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Unknown error',
        0,
        error
      );

      return {
        data: null,
        error: ebaaError,
        status: ebaaError.status,
        statusText: ebaaError.message
      };
    }
  }

  /**
   * Execute raw SQL query
   */
  async query<T = any>(sql: string, params?: any[]): Promise<RpcResponse<T[]>> {
    try {
      const endpoint = `/api/v1/database/query`;
      const response = await this.httpClient.post<T[]>(endpoint, {
        sql,
        params,
        workspaceId: this.options.workspaceId,
        schema: this.options.schema
      });

      if (response.error) {
        return {
          data: null,
          error: response.error,
          status: response.status,
          statusText: response.statusText
        };
      }

      return {
        data: response.data,
        error: null,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      const ebaaError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Unknown error',
        0,
        error
      );

      return {
        data: null,
        error: ebaaError,
        status: ebaaError.status,
        statusText: ebaaError.message
      };
    }
  }

  /**
   * Get table schema information
   */
  async getSchema(tableName?: string) {
    try {
      const endpoint = tableName 
        ? `/api/v1/database/schema/${this.options.schema}/${tableName}`
        : `/api/v1/database/schema/${this.options.schema}`;
        
      const response = await this.httpClient.get(endpoint, {
        params: { workspaceId: this.options.workspaceId }
      });

      return response;
    } catch (error) {
      const ebaaError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Unknown error',
        0,
        error
      );

      return {
        data: null,
        error: ebaaError,
        status: ebaaError.status,
        statusText: ebaaError.message
      };
    }
  }

  /**
   * Create a new table
   */
  async createTable(tableName: string, columns: Record<string, any>) {
    try {
      const endpoint = `/api/v1/database/schema/${this.options.schema}/tables`;
      const response = await this.httpClient.post(endpoint, {
        tableName,
        columns,
        workspaceId: this.options.workspaceId
      });

      return response;
    } catch (error) {
      const ebaaError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Unknown error',
        0,
        error
      );

      return {
        data: null,
        error: ebaaError,
        status: ebaaError.status,
        statusText: ebaaError.message
      };
    }
  }

  /**
   * Drop a table
   */
  async dropTable(tableName: string) {
    try {
      const endpoint = `/api/v1/database/schema/${this.options.schema}/tables/${tableName}`;
      const response = await this.httpClient.delete(endpoint, {
        params: { workspaceId: this.options.workspaceId }
      });

      return response;
    } catch (error) {
      const ebaaError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Unknown error',
        0,
        error
      );

      return {
        data: null,
        error: ebaaError,
        status: ebaaError.status,
        statusText: ebaaError.message
      };
    }
  }
}