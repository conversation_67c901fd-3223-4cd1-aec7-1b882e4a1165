import { HttpClient } from '../lib/HttpClient';
import { 
  QueryResponse, 
  InsertResponse, 
  UpdateResponse, 
  DeleteResponse,
  DatabaseFilter,
  DatabaseOrder,
  QueryOptions 
} from './types';
import { EBaaSError } from '../lib/EBaaSError';

export class QueryBuilder<T = any> {
  private httpClient: HttpClient;
  private tableName: string;
  private workspaceId: string;
  private schema: string;
  
  // Query building state
  private selectFields: string[] = ['*'];
  private filters: Array<{ column: string; operator: DatabaseFilter; value: any }> = [];
  private orderBy: Array<{ column: string; ascending: boolean; nullsFirst?: boolean }> = [];
  private limitValue?: number;
  private offsetValue?: number;
  private rangeStart?: number;
  private rangeEnd?: number;

  constructor(
    httpClient: HttpClient, 
    tableName: string, 
    workspaceId: string,
    schema: string = 'public'
  ) {
    this.httpClient = httpClient;
    this.tableName = tableName;
    this.workspaceId = workspaceId;
    this.schema = schema;
  }

  /**
   * Select specific columns
   * @param columns - Columns to select ('*' for all, or comma-separated list)
   */
  select(columns: string = '*'): QueryBuilder<T> {
    this.selectFields = columns === '*' ? ['*'] : columns.split(',').map(c => c.trim());
    return this;
  }

  /**
   * Filter rows based on column value
   */
  eq(column: string, value: any): QueryBuilder<T> {
    this.filters.push({ column, operator: 'eq', value });
    return this;
  }

  neq(column: string, value: any): QueryBuilder<T> {
    this.filters.push({ column, operator: 'neq', value });
    return this;
  }

  gt(column: string, value: any): QueryBuilder<T> {
    this.filters.push({ column, operator: 'gt', value });
    return this;
  }

  gte(column: string, value: any): QueryBuilder<T> {
    this.filters.push({ column, operator: 'gte', value });
    return this;
  }

  lt(column: string, value: any): QueryBuilder<T> {
    this.filters.push({ column, operator: 'lt', value });
    return this;
  }

  lte(column: string, value: any): QueryBuilder<T> {
    this.filters.push({ column, operator: 'lte', value });
    return this;
  }

  like(column: string, pattern: string): QueryBuilder<T> {
    this.filters.push({ column, operator: 'like', value: pattern });
    return this;
  }

  ilike(column: string, pattern: string): QueryBuilder<T> {
    this.filters.push({ column, operator: 'ilike', value: pattern });
    return this;
  }

  is(column: string, value: any): QueryBuilder<T> {
    this.filters.push({ column, operator: 'is', value });
    return this;
  }

  in(column: string, values: any[]): QueryBuilder<T> {
    this.filters.push({ column, operator: 'in', value: values });
    return this;
  }

  contains(column: string, value: any): QueryBuilder<T> {
    this.filters.push({ column, operator: 'contains', value });
    return this;
  }

  /**
   * Order results
   */
  order(column: string, options?: { ascending?: boolean; nullsFirst?: boolean }): QueryBuilder<T> {
    this.orderBy.push({
      column,
      ascending: options?.ascending !== false,
      nullsFirst: options?.nullsFirst
    });
    return this;
  }

  /**
   * Limit number of results
   */
  limit(count: number): QueryBuilder<T> {
    this.limitValue = count;
    return this;
  }

  /**
   * Skip number of results
   */
  offset(count: number): QueryBuilder<T> {
    this.offsetValue = count;
    return this;
  }

  /**
   * Limit to a range of rows
   */
  range(from: number, to: number): QueryBuilder<T> {
    this.rangeStart = from;
    this.rangeEnd = to;
    return this;
  }

  /**
   * Execute the query and return results
   */
  async execute(options?: QueryOptions): Promise<QueryResponse<T>> {
    try {
      const queryParams = this.buildQueryParams(options);
      const endpoint = `/api/v1/database/${this.schema}/${this.tableName}`;
      
      const response = await this.httpClient.get<T[]>(endpoint, {
        params: queryParams
      });

      if (response.error) {
        return {
          data: null,
          error: response.error,
          status: response.status,
          statusText: response.statusText
        };
      }

      return {
        data: response.data,
        error: null,
        status: response.status,
        statusText: response.statusText,
        count: Array.isArray(response.data) ? response.data.length : 0
      };
    } catch (error) {
      const ebaaError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Unknown error',
        0,
        error
      );

      return {
        data: null,
        error: ebaaError,
        status: ebaaError.status,
        statusText: ebaaError.message
      };
    }
  }

  /**
   * Insert new rows
   */
  async insert(data: Partial<T> | Partial<T>[]): Promise<InsertResponse<T>> {
    try {
      const endpoint = `/api/v1/database/${this.schema}/${this.tableName}`;
      const response = await this.httpClient.post<T[]>(endpoint, Array.isArray(data) ? data : [data]);

      if (response.error) {
        return {
          data: null,
          error: response.error,
          status: response.status,
          statusText: response.statusText
        };
      }

      return {
        data: response.data,
        error: null,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      const ebaaError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Unknown error',
        0,
        error
      );

      return {
        data: null,
        error: ebaaError,
        status: ebaaError.status,
        statusText: ebaaError.message
      };
    }
  }

  /**
   * Update existing rows
   */
  async update(data: Partial<T>): Promise<UpdateResponse<T>> {
    try {
      const queryParams = this.buildQueryParams();
      const endpoint = `/api/v1/database/${this.schema}/${this.tableName}`;
      
      const response = await this.httpClient.put<T[]>(endpoint, data, {
        params: queryParams
      });

      if (response.error) {
        return {
          data: null,
          error: response.error,
          status: response.status,
          statusText: response.statusText
        };
      }

      return {
        data: response.data,
        error: null,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      const ebaaError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Unknown error',
        0,
        error
      );

      return {
        data: null,
        error: ebaaError,
        status: ebaaError.status,
        statusText: ebaaError.message
      };
    }
  }

  /**
   * Delete rows
   */
  async delete(): Promise<DeleteResponse> {
    try {
      const queryParams = this.buildQueryParams();
      const endpoint = `/api/v1/database/${this.schema}/${this.tableName}`;
      
      const response = await this.httpClient.delete(endpoint, {
        params: queryParams
      });

      if (response.error) {
        return {
          data: null,
          error: response.error,
          status: response.status,
          statusText: response.statusText
        };
      }

      return {
        data: null,
        error: null,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      const ebaaError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Unknown error',
        0,
        error
      );

      return {
        data: null,
        error: ebaaError,
        status: ebaaError.status,
        statusText: ebaaError.message
      };
    }
  }

  private buildQueryParams(options?: QueryOptions): Record<string, any> {
    const params: Record<string, any> = {
      workspaceId: this.workspaceId
    };

    // Select fields
    if (this.selectFields.length > 0 && !this.selectFields.includes('*')) {
      params.select = this.selectFields.join(',');
    }

    // Filters
    this.filters.forEach((filter, index) => {
      params[`filter[${index}][column]`] = filter.column;
      params[`filter[${index}][operator]`] = filter.operator;
      params[`filter[${index}][value]`] = JSON.stringify(filter.value);
    });

    // Order by
    this.orderBy.forEach((order, index) => {
      params[`order[${index}][column]`] = order.column;
      params[`order[${index}][direction]`] = order.ascending ? 'asc' : 'desc';
      if (order.nullsFirst !== undefined) {
        params[`order[${index}][nullsFirst]`] = order.nullsFirst;
      }
    });

    // Limit and offset
    if (this.limitValue !== undefined) {
      params.limit = this.limitValue;
    }
    if (this.offsetValue !== undefined) {
      params.offset = this.offsetValue;
    }

    // Range
    if (this.rangeStart !== undefined && this.rangeEnd !== undefined) {
      params.rangeStart = this.rangeStart;
      params.rangeEnd = this.rangeEnd;
    }

    // Options
    if (options?.head) {
      params.head = true;
    }
    if (options?.count) {
      params.count = options.count;
    }

    return params;
  }
}