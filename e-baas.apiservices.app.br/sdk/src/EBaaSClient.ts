import { AuthClient } from './auth/AuthClient';
import { DatabaseClient } from './database/DatabaseClient';
import { StorageClient } from './storage/StorageClient';
import { RealtimeClient } from './realtime/RealtimeClient';
import { EdgeFunctionsClient } from './edge-functions/EdgeFunctionsClient';
import { HttpClient } from './lib/HttpClient';

export interface EBaaSClientOptions {
  url: string;
  apikey: string;
  workspaceId: string;
  schema?: string;
  headers?: Record<string, string>;
  autoRefreshToken?: boolean;
  persistSession?: boolean;
  detectSessionInUrl?: boolean;
  realtime?: {
    params?: Record<string, string>;
    heartbeatIntervalMs?: number;
    reconnectDelayMs?: number;
  };
}

/**
 * Main E-BaaS client class - entry point for all E-BaaS functionality
 * Similar to Supabase's createClient()
 */
export class EBaaSClient {
  public readonly supabaseUrl: string; // Keep supabase naming for compatibility
  public readonly supabaseKey: string; // Keep supabase naming for compatibility
  public readonly workspaceId: string;
  
  // Core clients
  public readonly auth: AuthClient;
  public readonly database: DatabaseClient; 
  public readonly storage: StorageClient;
  public readonly realtime: RealtimeClient;
  public readonly functions: EdgeFunctionsClient;
  
  private httpClient: HttpClient;

  constructor(options: EBaaSClientOptions) {
    this.supabaseUrl = options.url;
    this.supabaseKey = options.apikey;
    this.workspaceId = options.workspaceId;

    // Initialize HTTP client
    this.httpClient = new HttpClient({
      baseURL: options.url,
      headers: {
        'Authorization': `Bearer ${options.apikey}`,
        'X-Workspace-Id': options.workspaceId,
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    // Initialize service clients
    this.auth = new AuthClient(this.httpClient, {
      autoRefreshToken: options.autoRefreshToken ?? true,
      persistSession: options.persistSession ?? true,
      detectSessionInUrl: options.detectSessionInUrl ?? true
    });

    this.database = new DatabaseClient(this.httpClient, {
      schema: options.schema || 'public',
      workspaceId: this.workspaceId
    });

    this.storage = new StorageClient(this.httpClient, {
      workspaceId: this.workspaceId
    });

    this.realtime = new RealtimeClient(options.url, {
      apikey: options.apikey,
      workspaceId: this.workspaceId,
      params: options.realtime?.params,
      heartbeatIntervalMs: options.realtime?.heartbeatIntervalMs,
      reconnectDelayMs: options.realtime?.reconnectDelayMs
    });

    this.functions = new EdgeFunctionsClient(this.httpClient, {
      workspaceId: this.workspaceId
    });

    // Setup auth state synchronization
    this.auth.onAuthStateChange((event, session) => {
      if (session?.access_token) {
        // Update HTTP client auth header
        this.httpClient.setAuthToken(session.access_token);
        
        // Update realtime auth
        this.realtime.setAuth(session.access_token);
      }
    });
  }

  /**
   * Create a table query builder
   * Usage: client.from('users').select('*')
   */
  from(table: string) {
    return this.database.from(table);
  }

  /**
   * Execute raw SQL query
   * Usage: client.rpc('my_function', { param1: 'value' })
   */
  rpc(fn: string, params?: Record<string, any>) {
    return this.database.rpc(fn, params);
  }

  /**
   * Remove all subscriptions and close connections
   */
  removeAllChannels() {
    return this.realtime.removeAllChannels();
  }

  /**
   * Get a realtime channel
   * Usage: client.channel('public:users')
   */
  channel(name: string, options?: any) {
    return this.realtime.channel(name, options);
  }

  /**
   * Remove a realtime channel
   */
  removeChannel(channel: any) {
    return this.realtime.removeChannel(channel);
  }
}

/**
 * Create a new E-BaaS client instance
 * @param url - E-BaaS API URL
 * @param apikey - API key for authentication  
 * @param options - Additional client options
 */
export function createClient(
  url: string, 
  apikey: string, 
  options: Partial<EBaaSClientOptions> = {}
): EBaaSClient {
  const workspaceId = options.workspaceId || 'default';
  
  return new EBaaSClient({
    url,
    apikey,
    workspaceId,
    ...options
  });
}