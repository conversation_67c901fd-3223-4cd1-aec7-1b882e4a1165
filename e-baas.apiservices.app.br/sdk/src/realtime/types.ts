export interface RealtimeMessage {
  event: string;
  payload: any;
  ref?: string;
  topic: string;
}

export interface ChannelSubscription {
  unsubscribe: () => void;
}

export interface PresenceState {
  [key: string]: any;
}

export interface RealtimeEvent {
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  schema: string;
  record: any;
  old_record?: any;
}

export interface RealtimeClientOptions {
  apikey: string;
  workspaceId: string;
  params?: Record<string, string>;
  heartbeatIntervalMs?: number;
  reconnectDelayMs?: number;
  logger?: (kind: string, msg: string, data?: any) => void;
  encode?: (payload: any, callback: (encoded: string) => void) => void;
  decode?: (payload: string, callback: (decoded: any) => void) => void;
}

export interface ChannelOptions {
  config?: {
    broadcast?: { self?: boolean; ack?: boolean };
    presence?: { key?: string };
  };
}