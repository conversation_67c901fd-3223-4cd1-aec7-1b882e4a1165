import { io, Socket } from 'socket.io-client';
import { RealtimeChannel } from './RealtimeChannel';
import { RealtimeClientOptions, ChannelOptions } from './types';

export class RealtimeClient {
  private socket: Socket | null = null;
  private channels: Map<string, RealtimeChannel> = new Map();
  private url: string;
  private options: RealtimeClientOptions;
  private accessToken: string | null = null;

  constructor(url: string, options: RealtimeClientOptions) {
    this.url = url.replace(/^http/, 'ws'); // Convert HTTP to WebSocket URL
    this.options = options;
    this.accessToken = options.apikey;
  }

  /**
   * Set authentication token
   */
  setAuth(token: string): void {
    this.accessToken = token;
    
    if (this.socket) {
      this.socket.auth = { token };
    }
  }

  /**
   * Connect to the realtime server
   */
  connect(): void {
    if (this.socket?.connected) {
      return;
    }

    this.socket = io(this.url, {
      auth: {
        token: this.accessToken,
        workspaceId: this.options.workspaceId
      },
      query: this.options.params,
      transports: ['websocket'],
      autoConnect: true,
      reconnection: true,
      reconnectionDelay: this.options.reconnectDelayMs || 1000,
      timeout: 20000
    });

    this.setupEventHandlers();
  }

  /**
   * Disconnect from the realtime server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    // Clean up channels
    this.channels.forEach(channel => {
      channel.unsubscribe();
    });
    this.channels.clear();
  }

  /**
   * Get or create a channel
   */
  channel(topic: string, options?: ChannelOptions): RealtimeChannel {
    if (!this.channels.has(topic)) {
      const channel = new RealtimeChannel(topic, this.socket);
      this.channels.set(topic, channel);
      
      // Auto-connect if not connected
      if (!this.socket?.connected) {
        this.connect();
      }
    }

    return this.channels.get(topic)!;
  }

  /**
   * Remove a channel
   */
  removeChannel(channel: RealtimeChannel): void {
    channel.unsubscribe();
    this.channels.delete(channel.topic);
  }

  /**
   * Remove all channels
   */
  removeAllChannels(): void {
    this.channels.forEach(channel => {
      channel.unsubscribe();
    });
    this.channels.clear();
  }

  /**
   * Get connection status
   */
  get isConnected(): boolean {
    return this.socket?.connected || false;
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      if (this.options.logger) {
        this.options.logger('info', 'Connected to realtime server');
      }
    });

    this.socket.on('disconnect', (reason) => {
      if (this.options.logger) {
        this.options.logger('info', 'Disconnected from realtime server', { reason });
      }
    });

    this.socket.on('error', (error) => {
      if (this.options.logger) {
        this.options.logger('error', 'Realtime connection error', { error });
      }
    });

    // Handle channel-specific events
    this.socket.on('postgres_changes', (data) => {
      const { topic, payload } = data;
      const channel = this.channels.get(topic);
      if (channel) {
        channel._trigger(`postgres_changes:${JSON.stringify(payload.filter || {})}`, payload);
      }
    });

    this.socket.on('broadcast', (data) => {
      const { topic, event, payload } = data;
      const channel = this.channels.get(topic);
      if (channel) {
        channel._trigger(`broadcast:${JSON.stringify({ event })}`, payload);
      }
    });

    this.socket.on('presence', (data) => {
      const { topic, event, payload } = data;
      const channel = this.channels.get(topic);
      if (channel) {
        channel._trigger(`presence:${JSON.stringify({ event })}`, payload);
      }
    });

    // Handle channel join/leave responses
    this.socket.on('join_reply', (data) => {
      const { topic, status } = data;
      const channel = this.channels.get(topic);
      if (channel) {
        if (status === 'ok') {
          channel._setState('joined');
        } else {
          channel._setState('errored');
        }
      }
    });

    this.socket.on('leave_reply', (data) => {
      const { topic } = data;
      const channel = this.channels.get(topic);
      if (channel) {
        channel._setState('closed');
      }
    });

    // Setup heartbeat
    if (this.options.heartbeatIntervalMs) {
      setInterval(() => {
        if (this.socket?.connected) {
          this.socket.emit('heartbeat', { timestamp: Date.now() });
        }
      }, this.options.heartbeatIntervalMs);
    }
  }
}