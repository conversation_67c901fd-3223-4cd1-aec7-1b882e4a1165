import { EventEmitter } from 'eventemitter3';
import { ChannelSubscription, PresenceState, RealtimeEvent } from './types';

export class RealtimeChannel extends EventEmitter {
  public topic: string;
  private socket: any; // Will be the actual socket connection
  private joinedOnce: boolean = false;
  private state: 'closed' | 'errored' | 'joined' | 'joining' | 'leaving' = 'closed';

  constructor(topic: string, socket: any) {
    super();
    this.topic = topic;
    this.socket = socket;
  }

  /**
   * Subscribe to postgres changes
   */
  onPostgresChanges(
    filter: { 
      event: '*' | 'INSERT' | 'UPDATE' | 'DELETE';
      schema: string;
      table?: string;
      filter?: string;
    },
    callback: (payload: RealtimeEvent) => void
  ): ChannelSubscription {
    const eventKey = `postgres_changes:${JSON.stringify(filter)}`;
    super.on(eventKey, callback);

    return {
      unsubscribe: () => {
        super.off(eventKey, callback);
      }
    };
  }
  
  /**
   * Subscribe to broadcast events
   */
  onBroadcast(
    filter: { event: string },
    callback: (payload: any) => void
  ): ChannelSubscription {
    const eventKey = `broadcast:${JSON.stringify(filter)}`;
    super.on(eventKey, callback);

    return {
      unsubscribe: () => {
        super.off(eventKey, callback);
      }
    };
  }

  /**
   * Subscribe to presence events
   */
  onPresence(
    filter: { event: 'sync' | 'join' | 'leave' },
    callback: (payload: any) => void
  ): ChannelSubscription {
    const eventKey = `presence:${JSON.stringify(filter)}`;
    super.on(eventKey, callback);

    return {
      unsubscribe: () => {
        super.off(eventKey, callback);
      }
    };
  }

  /**
   * Send a broadcast message
   */
  send(payload: { type: string; event: string; [key: string]: any }): Promise<'ok' | 'error' | 'timeout'> {
    return new Promise((resolve) => {
      if (this.socket && this.state === 'joined') {
        this.socket.emit('broadcast', {
          topic: this.topic,
          event: payload.event,
          payload: payload,
          ref: this.makeRef()
        });
        resolve('ok');
      } else {
        resolve('error');
      }
    });
  }

  /**
   * Track presence
   */
  track(payload: Record<string, any>): Promise<'ok' | 'error' | 'timeout'> {
    return new Promise((resolve) => {
      if (this.socket && this.state === 'joined') {
        this.socket.emit('presence', {
          topic: this.topic,
          event: 'track',
          payload: payload,
          ref: this.makeRef()
        });
        resolve('ok');
      } else {
        resolve('error');
      }
    });
  }

  /**
   * Untrack presence
   */
  untrack(): Promise<'ok' | 'error' | 'timeout'> {
    return new Promise((resolve) => {
      if (this.socket && this.state === 'joined') {
        this.socket.emit('presence', {
          topic: this.topic,
          event: 'untrack',
          ref: this.makeRef()
        });
        resolve('ok');
      } else {
        resolve('error');
      }
    });
  }

  /**
   * Subscribe to the channel
   */
  subscribe(callback?: (status: 'SUBSCRIBED' | 'CHANNEL_ERROR' | 'TIMED_OUT' | 'CLOSED') => void): RealtimeChannel {
    if (!this.joinedOnce) {
      this.joinedOnce = true;
      this.rejoin();
    }

    if (callback) {
      super.on('status', callback);
    }

    return this;
  }

  /**
   * Unsubscribe from the channel
   */
  unsubscribe(): Promise<'ok' | 'error' | 'timeout'> {
    return new Promise((resolve) => {
      this.state = 'leaving';
      
      if (this.socket) {
        this.socket.emit('leave', {
          topic: this.topic,
          ref: this.makeRef()
        });
      }

      this.state = 'closed';
      this.removeAllListeners();
      resolve('ok');
    });
  }

  private rejoin(): void {
    if (this.state === 'leaving') return;
    
    this.state = 'joining';
    
    if (this.socket) {
      this.socket.emit('join', {
        topic: this.topic,
        ref: this.makeRef()
      });
    }
  }

  private makeRef(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  // Internal methods for socket integration
  _trigger(event: string, payload: any): void {
    this.emit(event, payload);
  }

  _setState(state: typeof this.state): void {
    this.state = state;
    this.emit('status', state.toUpperCase());
  }
}