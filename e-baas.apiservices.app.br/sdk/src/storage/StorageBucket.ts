import { HttpClient } from '../lib/HttpClient';
import { EBaaSError } from '../lib/EBaaSError';
import { 
  FileObject, 
  UploadResponse, 
  DownloadResponse, 
  SignedUrlResponse,
  ListResponse,
  UploadOptions,
  DownloadOptions,
  ListOptions,
  SignedUrlOptions,
  StorageClientOptions 
} from './types';

export class StorageBucket {
  private httpClient: HttpClient;
  private bucketName: string;
  private options: StorageClientOptions;

  constructor(
    httpClient: HttpClient, 
    bucketName: string, 
    options: StorageClientOptions
  ) {
    this.httpClient = httpClient;
    this.bucketName = bucketName;
    this.options = options;
  }

  /**
   * Upload a file to the bucket
   */
  async upload(
    path: string, 
    file: File | Blob | ArrayBuffer | string, 
    options?: UploadOptions
  ): Promise<UploadResponse> {
    try {
      const formData = new FormData();
      
      // Convert different file types to blob
      let blob: Blob;
      if (file instanceof File) {
        blob = file;
        formData.append('file', file, path.split('/').pop());
      } else if (file instanceof Blob) {
        blob = file;
        formData.append('file', blob, path.split('/').pop());
      } else if (file instanceof ArrayBuffer) {
        blob = new Blob([file]);
        formData.append('file', blob, path.split('/').pop());
      } else if (typeof file === 'string') {
        blob = new Blob([file], { type: 'text/plain' });
        formData.append('file', blob, path.split('/').pop());
      } else {
        throw new EBaaSError('Unsupported file type', 400);
      }

      // Add metadata
      formData.append('bucketName', this.bucketName);
      formData.append('workspaceId', this.options.workspaceId);
      formData.append('path', path);
      
      if (options?.contentType) {
        formData.append('contentType', options.contentType);
      }
      if (options?.cacheControl) {
        formData.append('cacheControl', options.cacheControl);
      }
      if (options?.metadata) {
        formData.append('metadata', JSON.stringify(options.metadata));
      }
      if (options?.upsert !== undefined) {
        formData.append('overwrite', options.upsert.toString());
      }

      const response = await this.httpClient.post<{ file: FileObject }>(
        '/api/v1/storage/v1/object',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data?.file || null,
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Upload failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Download a file from the bucket
   */
  async download(path: string, options?: DownloadOptions): Promise<DownloadResponse> {
    try {
      const params: Record<string, any> = {
        bucketName: this.bucketName,
        workspaceId: this.options.workspaceId
      };

      // Add transform parameters
      if (options?.transform) {
        const transform = options.transform;
        const transformParams: string[] = [];
        
        if (transform.width) transformParams.push(`w=${transform.width}`);
        if (transform.height) transformParams.push(`h=${transform.height}`);
        if (transform.quality) transformParams.push(`q=${transform.quality}`);
        if (transform.format) transformParams.push(`f=${transform.format}`);
        if (transform.resize) transformParams.push(`r=${transform.resize}`);
        
        if (transformParams.length > 0) {
          params.transform = transformParams.join(',');
        }
      }

      const response = await this.httpClient.axios.get(
        `/api/v1/storage/v1/object/${this.bucketName}/${path}`,
        {
          params,
          responseType: 'blob'
        }
      );

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Download failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Delete a file from the bucket
   */
  async remove(paths: string | string[]): Promise<{ data: { message: string } | null; error: Error | null }> {
    try {
      const pathsArray = Array.isArray(paths) ? paths : [paths];
      
      const response = await this.httpClient.delete('/api/v1/storage/v1/object', {
        data: {
          bucketName: this.bucketName,
          workspaceId: this.options.workspaceId,
          paths: pathsArray
        }
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data || { message: 'Files deleted successfully' },
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Delete failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * List files in the bucket
   */
  async list(path?: string, options?: ListOptions): Promise<ListResponse> {
    try {
      const params: Record<string, any> = {
        bucketName: this.bucketName,
        workspaceId: this.options.workspaceId
      };

      if (path) {
        params.prefix = path;
      }
      if (options?.limit) {
        params.limit = options.limit;
      }
      if (options?.offset) {
        params.offset = options.offset;
      }
      if (options?.sortBy) {
        params.sortBy = options.sortBy;
      }
      if (options?.search) {
        params.search = options.search;
      }

      const response = await this.httpClient.get<{
        files: FileObject[];
        hasMore: boolean;
        nextOffset?: string;
      }>('/api/v1/storage/v1/object/list', { params });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data?.files || [],
        error: null,
        hasMore: response.data?.hasMore,
        nextOffset: response.data?.nextOffset
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'List failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Create a signed URL for a file
   */
  async createSignedUrl(path: string, expiresIn: number, options?: Omit<SignedUrlOptions, 'expiresIn'>): Promise<SignedUrlResponse> {
    try {
      const requestData: Record<string, any> = {
        bucketName: this.bucketName,
        workspaceId: this.options.workspaceId,
        filePath: path,
        operation: 'download',
        expiresIn
      };

      // Add transform options
      if (options?.transform) {
        requestData.options = {
          transform: options.transform
        };
      }

      const response = await this.httpClient.post<{
        signedUrl: string;
        token: string;
        expiresAt: string;
      }>('/api/v1/storage/v1/object/sign', requestData);

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Signed URL creation failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Create a signed upload URL
   */
  async createSignedUploadUrl(path: string, options?: UploadOptions): Promise<SignedUrlResponse> {
    try {
      const requestData: Record<string, any> = {
        bucketName: this.bucketName,
        workspaceId: this.options.workspaceId,
        filePath: path,
        operation: 'upload',
        expiresIn: 3600 // 1 hour default
      };

      if (options) {
        requestData.options = options;
      }

      const response = await this.httpClient.post<{
        signedUrl: string;
        token: string;
        expiresAt: string;
      }>('/api/v1/storage/v1/object/sign', requestData);

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Signed upload URL creation failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Get public URL for a file (if bucket is public)
   */
  getPublicUrl(path: string, options?: DownloadOptions): { data: { publicUrl: string } } {
    let url = `/api/v1/storage/v1/object/public/${this.bucketName}/${path}`;
    
    if (options?.transform) {
      const transform = options.transform;
      const transformParams: string[] = [];
      
      if (transform.width) transformParams.push(`w=${transform.width}`);
      if (transform.height) transformParams.push(`h=${transform.height}`);
      if (transform.quality) transformParams.push(`q=${transform.quality}`);
      if (transform.format) transformParams.push(`f=${transform.format}`);
      if (transform.resize) transformParams.push(`r=${transform.resize}`);
      
      if (transformParams.length > 0) {
        url += `?transform=${transformParams.join(',')}`;
      }
    }

    return {
      data: { publicUrl: url }
    };
  }

  /**
   * Copy a file within the bucket
   */
  async copy(fromPath: string, toPath: string): Promise<{ data: { message: string } | null; error: Error | null }> {
    try {
      const response = await this.httpClient.post('/api/v1/storage/v1/object/copy', {
        bucketName: this.bucketName,
        workspaceId: this.options.workspaceId,
        fromPath,
        toPath
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data || { message: 'File copied successfully' },
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Copy failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Move a file within the bucket
   */
  async move(fromPath: string, toPath: string): Promise<{ data: { message: string } | null; error: Error | null }> {
    try {
      const response = await this.httpClient.post('/api/v1/storage/v1/object/move', {
        bucketName: this.bucketName,
        workspaceId: this.options.workspaceId,
        fromPath,
        toPath
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data || { message: 'File moved successfully' },
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Move failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }
}