import { HttpClient } from '../lib/HttpClient';
import { StorageBucket } from './StorageBucket';
import { EBaaSError } from '../lib/EBaaSError';
import { BucketInfo, BucketOptions, StorageClientOptions } from './types';

export class StorageClient {
  private httpClient: HttpClient;
  private options: StorageClientOptions;
  private buckets: Map<string, StorageBucket> = new Map();

  constructor(httpClient: HttpClient, options: StorageClientOptions) {
    this.httpClient = httpClient;
    this.options = options;
  }

  /**
   * Get a reference to a storage bucket
   */
  from(bucketName: string): StorageBucket {
    if (!this.buckets.has(bucketName)) {
      this.buckets.set(bucketName, new StorageBucket(this.httpClient, bucketName, this.options));
    }
    return this.buckets.get(bucketName)!;
  }

  /**
   * Create a new bucket
   */
  async createBucket(name: string, options?: BucketOptions): Promise<{ data: BucketInfo | null; error: Error | null }> {
    try {
      const response = await this.httpClient.post<BucketInfo>('/api/v1/storage/v1/bucket', {
        name,
        workspaceId: this.options.workspaceId,
        public: options?.public || false,
        allowedMimeTypes: options?.allowedMimeTypes,
        fileSizeLimit: options?.fileSizeLimit
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Bucket creation failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Get bucket information
   */
  async getBucket(name: string): Promise<{ data: BucketInfo | null; error: Error | null }> {
    try {
      const response = await this.httpClient.get<BucketInfo>(`/api/v1/storage/v1/bucket/${name}`, {
        params: { workspaceId: this.options.workspaceId }
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Failed to get bucket',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * List all buckets
   */
  async listBuckets(): Promise<{ data: BucketInfo[] | null; error: Error | null }> {
    try {
      const response = await this.httpClient.get<BucketInfo[]>('/api/v1/storage/v1/bucket', {
        params: { workspaceId: this.options.workspaceId }
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data || [],
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Failed to list buckets',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Update bucket settings
   */
  async updateBucket(name: string, options: BucketOptions): Promise<{ data: BucketInfo | null; error: Error | null }> {
    try {
      const response = await this.httpClient.put<BucketInfo>(`/api/v1/storage/v1/bucket/${name}`, {
        workspaceId: this.options.workspaceId,
        ...options
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Bucket update failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Delete a bucket
   */
  async deleteBucket(name: string, force: boolean = false): Promise<{ data: { message: string } | null; error: Error | null }> {
    try {
      const response = await this.httpClient.delete(`/api/v1/storage/v1/bucket/${name}`, {
        params: { 
          workspaceId: this.options.workspaceId,
          force: force.toString()
        }
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      // Remove bucket from cache
      this.buckets.delete(name);

      return {
        data: response.data || { message: 'Bucket deleted successfully' },
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Bucket deletion failed',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Empty a bucket (delete all files)
   */
  async emptyBucket(name: string): Promise<{ data: { message: string } | null; error: Error | null }> {
    try {
      const response = await this.httpClient.delete(`/api/v1/storage/v1/bucket/${name}/empty`, {
        params: { workspaceId: this.options.workspaceId }
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data || { message: 'Bucket emptied successfully' },
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Failed to empty bucket',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }

  /**
   * Get storage usage statistics
   */
  async getUsage(): Promise<{ 
    data: { 
      totalBuckets: number; 
      totalFiles: number; 
      totalSize: number; 
      buckets: Array<{ name: string; filesCount: number; size: number }> 
    } | null; 
    error: Error | null 
  }> {
    try {
      const response = await this.httpClient.get('/api/v1/storage/v1/usage', {
        params: { workspaceId: this.options.workspaceId }
      });

      if (response.error) {
        return {
          data: null,
          error: response.error
        };
      }

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      const storageError = error instanceof EBaaSError ? error : new EBaaSError(
        error instanceof Error ? error.message : 'Failed to get usage statistics',
        0,
        error
      );

      return {
        data: null,
        error: storageError
      };
    }
  }
}