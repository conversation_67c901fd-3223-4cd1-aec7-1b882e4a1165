{"name": "@e-baas/sdk", "version": "1.0.0", "description": "TypeScript client for E-BaaS - Enterprise Backend as a Service", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md"], "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "tsc --watch", "prepublishOnly": "npm run build", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["e-baas", "backend-as-a-service", "typescript", "sdk", "supabase-alternative", "database", "storage", "auth", "realtime"], "author": "E-BaaS Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/e-baas/sdk"}, "dependencies": {"axios": "^1.9.0", "socket.io-client": "^4.8.1", "eventemitter3": "^5.0.1"}, "devDependencies": {"@types/node": "^20.14.0", "typescript": "^5.4.0", "jest": "^29.7.0", "@types/jest": "^29.5.12", "ts-jest": "^29.2.5"}, "peerDependencies": {"typescript": ">=4.5.0"}, "engines": {"node": ">=18.0.0"}}