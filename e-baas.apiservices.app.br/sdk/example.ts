import { createClient } from './src/index';

// Example usage of the E-BaaS SDK
async function exampleUsage() {
  // Initialize the client
  const ebaas = createClient(
    'https://your-project.e-baas.io',
    'your-anon-key',
    {
      workspaceId: 'your-workspace-id'
    }
  );

  // Authentication Examples
  console.log('=== Authentication Examples ===');
  
  // Sign up
  const { data: signUpData, error: signUpError } = await ebaas.auth.signUp({
    email: '<EMAIL>',
    password: 'password123',
    name: '<PERSON>'
  });

  // Sign in
  const { data: signInData, error: signInError } = await ebaas.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'password123'
  });

  // Listen to auth changes
  ebaas.auth.onAuthStateChange((event, session) => {
    console.log('Auth state changed:', event, session?.user?.email);
  });

  // Database Examples
  console.log('=== Database Examples ===');
  
  // Insert data
  const { data: insertData, error: insertError } = await ebaas
    .from('users')
    .insert({ name: '<PERSON>', email: '<EMAIL>' });

  // Query data
  const { data: users, error: queryError } = await ebaas
    .from('users')
    .select('*')
    .eq('email', '<EMAIL>')
    .execute();

  // Update data
  const { data: updateData, error: updateError } = await ebaas
    .from('users')
    .update({ name: 'John Smith' })
    .eq('id', 1);

  // Delete data
  const { data: deleteData, error: deleteError } = await ebaas
    .from('users')
    .delete()
    .eq('id', 1);

  // RPC (Remote Procedure Call)
  const { data: rpcData, error: rpcError } = await ebaas.rpc('get_user_stats', {
    user_id: 1
  });

  // Storage Examples
  console.log('=== Storage Examples ===');
  
  // Create a bucket
  const { data: bucket, error: bucketError } = await ebaas.storage.createBucket('avatars', {
    public: true,
    fileSizeLimit: 5 * 1024 * 1024 // 5MB
  });

  // Upload a file
  const file = new File(['Hello World'], 'hello.txt', { type: 'text/plain' });
  const { data: uploadData, error: uploadError } = await ebaas.storage
    .from('avatars')
    .upload('folder/hello.txt', file);

  // Download a file
  const { data: downloadData, error: downloadError } = await ebaas.storage
    .from('avatars')
    .download('folder/hello.txt');

  // Get public URL
  const { data: publicUrlData } = ebaas.storage
    .from('avatars')
    .getPublicUrl('folder/hello.txt');

  // Create signed URL
  const { data: signedUrlData, error: signedUrlError } = await ebaas.storage
    .from('avatars')
    .createSignedUrl('folder/hello.txt', 3600); // 1 hour

  // List files
  const { data: files, error: listError } = await ebaas.storage
    .from('avatars')
    .list('folder/');

  // Realtime Examples
  console.log('=== Realtime Examples ===');
  
  // Subscribe to database changes
  const channel = ebaas.channel('public:users');
  
  // Listen to postgres changes
  channel.onPostgresChanges(
    {
      event: '*',
      schema: 'public',
      table: 'users'
    },
    (payload) => {
      console.log('Database change:', payload);
    }
  );

  // Send broadcast messages
  channel.send({
    type: 'broadcast',
    event: 'user_activity',
    payload: { user_id: 1, action: 'login' }
  });

  // Track presence
  channel.track({ user_id: 1, status: 'online' });

  // Subscribe to the channel
  channel.subscribe((status) => {
    console.log('Channel status:', status);
  });

  // Edge Functions Examples
  console.log('=== Edge Functions Examples ===');
  
  // Invoke a function
  const { data: functionData, error: functionError } = await ebaas.functions.invoke('hello-world', {
    body: { name: 'John' },
    headers: { 'Content-Type': 'application/json' }
  });

  // List functions
  const { data: functionsList, error: functionsListError } = await ebaas.functions.list();

  // Deploy a function
  const functionCode = `
    export default async function handler(req: Request) {
      const { name } = await req.json();
      return new Response(JSON.stringify({ message: \`Hello \${name}!\` }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
  `;

  const { data: deployData, error: deployError } = await ebaas.functions.deploy(
    'hello-world',
    functionCode,
    { description: 'A simple hello world function' }
  );
}

// Error handling utility
function handleError(error: any, operation: string) {
  if (error) {
    console.error(`Error in ${operation}:`, error.message);
    if (error.details) {
      console.error('Details:', error.details);
    }
  }
}

// Run the example (commented out to avoid execution)
// exampleUsage().catch(console.error);

export default exampleUsage;