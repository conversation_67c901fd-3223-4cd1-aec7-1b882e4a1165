"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EBaaSClient = void 0;
exports.createClient = createClient;
const AuthClient_1 = require("./auth/AuthClient");
const DatabaseClient_1 = require("./database/DatabaseClient");
const StorageClient_1 = require("./storage/StorageClient");
const RealtimeClient_1 = require("./realtime/RealtimeClient");
const EdgeFunctionsClient_1 = require("./edge-functions/EdgeFunctionsClient");
const HttpClient_1 = require("./lib/HttpClient");
/**
 * Main E-BaaS client class - entry point for all E-BaaS functionality
 * Similar to Supabase's createClient()
 */
class EBaaSClient {
    constructor(options) {
        this.supabaseUrl = options.url;
        this.supabaseKey = options.apikey;
        this.workspaceId = options.workspaceId;
        // Initialize HTTP client
        this.httpClient = new HttpClient_1.HttpClient({
            baseURL: options.url,
            headers: {
                'Authorization': `Bearer ${options.apikey}`,
                'X-Workspace-Id': options.workspaceId,
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
        // Initialize service clients
        this.auth = new AuthClient_1.AuthClient(this.httpClient, {
            autoRefreshToken: options.autoRefreshToken ?? true,
            persistSession: options.persistSession ?? true,
            detectSessionInUrl: options.detectSessionInUrl ?? true
        });
        this.database = new DatabaseClient_1.DatabaseClient(this.httpClient, {
            schema: options.schema || 'public',
            workspaceId: this.workspaceId
        });
        this.storage = new StorageClient_1.StorageClient(this.httpClient, {
            workspaceId: this.workspaceId
        });
        this.realtime = new RealtimeClient_1.RealtimeClient(options.url, {
            apikey: options.apikey,
            workspaceId: this.workspaceId,
            params: options.realtime?.params,
            heartbeatIntervalMs: options.realtime?.heartbeatIntervalMs,
            reconnectDelayMs: options.realtime?.reconnectDelayMs
        });
        this.functions = new EdgeFunctionsClient_1.EdgeFunctionsClient(this.httpClient, {
            workspaceId: this.workspaceId
        });
        // Setup auth state synchronization
        this.auth.onAuthStateChange((event, session) => {
            if (session?.access_token) {
                // Update HTTP client auth header
                this.httpClient.setAuthToken(session.access_token);
                // Update realtime auth
                this.realtime.setAuth(session.access_token);
            }
        });
    }
    /**
     * Create a table query builder
     * Usage: client.from('users').select('*')
     */
    from(table) {
        return this.database.from(table);
    }
    /**
     * Execute raw SQL query
     * Usage: client.rpc('my_function', { param1: 'value' })
     */
    rpc(fn, params) {
        return this.database.rpc(fn, params);
    }
    /**
     * Remove all subscriptions and close connections
     */
    removeAllChannels() {
        return this.realtime.removeAllChannels();
    }
    /**
     * Get a realtime channel
     * Usage: client.channel('public:users')
     */
    channel(name, options) {
        return this.realtime.channel(name, options);
    }
    /**
     * Remove a realtime channel
     */
    removeChannel(channel) {
        return this.realtime.removeChannel(channel);
    }
}
exports.EBaaSClient = EBaaSClient;
/**
 * Create a new E-BaaS client instance
 * @param url - E-BaaS API URL
 * @param apikey - API key for authentication
 * @param options - Additional client options
 */
function createClient(url, apikey, options = {}) {
    const workspaceId = options.workspaceId || 'default';
    return new EBaaSClient({
        url,
        apikey,
        workspaceId,
        ...options
    });
}
//# sourceMappingURL=EBaaSClient.js.map