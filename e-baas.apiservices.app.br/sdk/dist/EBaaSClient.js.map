{"version": 3, "file": "EBaaSClient.js", "sourceRoot": "", "sources": ["../src/EBaaSClient.ts"], "names": [], "mappings": ";;;AA8IA,oCAaC;AA3JD,kDAA+C;AAC/C,8DAA2D;AAC3D,2DAAwD;AACxD,8DAA2D;AAC3D,8EAA2E;AAC3E,iDAA8C;AAkB9C;;;GAGG;AACH,MAAa,WAAW;IActB,YAAY,OAA2B;QACrC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAEvC,yBAAyB;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,uBAAU,CAAC;YAC/B,OAAO,EAAE,OAAO,CAAC,GAAG;YACpB,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,OAAO,CAAC,MAAM,EAAE;gBAC3C,gBAAgB,EAAE,OAAO,CAAC,WAAW;gBACrC,cAAc,EAAE,kBAAkB;gBAClC,GAAG,OAAO,CAAC,OAAO;aACnB;SACF,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,uBAAU,CAAC,IAAI,CAAC,UAAU,EAAE;YAC1C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,IAAI;YAClD,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,IAAI;YAC9C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,IAAI,IAAI;SACvD,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,GAAG,IAAI,+BAAc,CAAC,IAAI,CAAC,UAAU,EAAE;YAClD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,QAAQ;YAClC,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG,IAAI,6BAAa,CAAC,IAAI,CAAC,UAAU,EAAE;YAChD,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,GAAG,IAAI,+BAAc,CAAC,OAAO,CAAC,GAAG,EAAE;YAC9C,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,MAAM;YAChC,mBAAmB,EAAE,OAAO,CAAC,QAAQ,EAAE,mBAAmB;YAC1D,gBAAgB,EAAE,OAAO,CAAC,QAAQ,EAAE,gBAAgB;SACrD,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,IAAI,yCAAmB,CAAC,IAAI,CAAC,UAAU,EAAE;YACxD,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC7C,IAAI,OAAO,EAAE,YAAY,EAAE,CAAC;gBAC1B,iCAAiC;gBACjC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAEnD,uBAAuB;gBACvB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,IAAI,CAAC,KAAa;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,GAAG,CAAC,EAAU,EAAE,MAA4B;QAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,IAAY,EAAE,OAAa;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,OAAY;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;CACF;AA3GD,kCA2GC;AAED;;;;;GAKG;AACH,SAAgB,YAAY,CAC1B,GAAW,EACX,MAAc,EACd,UAAuC,EAAE;IAEzC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,SAAS,CAAC;IAErD,OAAO,IAAI,WAAW,CAAC;QACrB,GAAG;QACH,MAAM;QACN,WAAW;QACX,GAAG,OAAO;KACX,CAAC,CAAC;AACL,CAAC"}