"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EBaaSError = exports.EdgeFunctionsClient = exports.RealtimeChannel = exports.RealtimeClient = exports.StorageBucket = exports.StorageClient = exports.QueryBuilder = exports.DatabaseClient = exports.AuthClient = exports.createClient = exports.EBaaSClient = void 0;
// Main E-BaaS SDK exports
var EBaaSClient_1 = require("./EBaaSClient");
Object.defineProperty(exports, "EBaaSClient", { enumerable: true, get: function () { return EBaaSClient_1.EBaaSClient; } });
Object.defineProperty(exports, "createClient", { enumerable: true, get: function () { return EBaaSClient_1.createClient; } });
// Auth module
var AuthClient_1 = require("./auth/AuthClient");
Object.defineProperty(exports, "AuthClient", { enumerable: true, get: function () { return AuthClient_1.AuthClient; } });
// Database module  
var DatabaseClient_1 = require("./database/DatabaseClient");
Object.defineProperty(exports, "DatabaseClient", { enumerable: true, get: function () { return DatabaseClient_1.DatabaseClient; } });
var QueryBuilder_1 = require("./database/QueryBuilder");
Object.defineProperty(exports, "QueryBuilder", { enumerable: true, get: function () { return QueryBuilder_1.QueryBuilder; } });
// Storage module
var StorageClient_1 = require("./storage/StorageClient");
Object.defineProperty(exports, "StorageClient", { enumerable: true, get: function () { return StorageClient_1.StorageClient; } });
var StorageBucket_1 = require("./storage/StorageBucket");
Object.defineProperty(exports, "StorageBucket", { enumerable: true, get: function () { return StorageBucket_1.StorageBucket; } });
// Realtime module
var RealtimeClient_1 = require("./realtime/RealtimeClient");
Object.defineProperty(exports, "RealtimeClient", { enumerable: true, get: function () { return RealtimeClient_1.RealtimeClient; } });
var RealtimeChannel_1 = require("./realtime/RealtimeChannel");
Object.defineProperty(exports, "RealtimeChannel", { enumerable: true, get: function () { return RealtimeChannel_1.RealtimeChannel; } });
// Edge Functions module
var EdgeFunctionsClient_1 = require("./edge-functions/EdgeFunctionsClient");
Object.defineProperty(exports, "EdgeFunctionsClient", { enumerable: true, get: function () { return EdgeFunctionsClient_1.EdgeFunctionsClient; } });
var EBaaSError_1 = require("./lib/EBaaSError");
Object.defineProperty(exports, "EBaaSError", { enumerable: true, get: function () { return EBaaSError_1.EBaaSError; } });
//# sourceMappingURL=index.js.map