import { HttpClient } from '../lib/HttpClient';
import { StorageBucket } from './StorageBucket';
import { BucketInfo, BucketOptions, StorageClientOptions } from './types';
export declare class StorageClient {
    private httpClient;
    private options;
    private buckets;
    constructor(httpClient: HttpClient, options: StorageClientOptions);
    /**
     * Get a reference to a storage bucket
     */
    from(bucketName: string): StorageBucket;
    /**
     * Create a new bucket
     */
    createBucket(name: string, options?: BucketOptions): Promise<{
        data: BucketInfo | null;
        error: Error | null;
    }>;
    /**
     * Get bucket information
     */
    getBucket(name: string): Promise<{
        data: BucketInfo | null;
        error: Error | null;
    }>;
    /**
     * List all buckets
     */
    listBuckets(): Promise<{
        data: BucketInfo[] | null;
        error: Error | null;
    }>;
    /**
     * Update bucket settings
     */
    updateBucket(name: string, options: BucketOptions): Promise<{
        data: BucketInfo | null;
        error: Error | null;
    }>;
    /**
     * Delete a bucket
     */
    deleteBucket(name: string, force?: boolean): Promise<{
        data: {
            message: string;
        } | null;
        error: Error | null;
    }>;
    /**
     * Empty a bucket (delete all files)
     */
    emptyBucket(name: string): Promise<{
        data: {
            message: string;
        } | null;
        error: Error | null;
    }>;
    /**
     * Get storage usage statistics
     */
    getUsage(): Promise<{
        data: {
            totalBuckets: number;
            totalFiles: number;
            totalSize: number;
            buckets: Array<{
                name: string;
                filesCount: number;
                size: number;
            }>;
        } | null;
        error: Error | null;
    }>;
}
//# sourceMappingURL=StorageClient.d.ts.map