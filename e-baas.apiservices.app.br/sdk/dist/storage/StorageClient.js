"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageClient = void 0;
const StorageBucket_1 = require("./StorageBucket");
const EBaaSError_1 = require("../lib/EBaaSError");
class StorageClient {
    constructor(httpClient, options) {
        this.buckets = new Map();
        this.httpClient = httpClient;
        this.options = options;
    }
    /**
     * Get a reference to a storage bucket
     */
    from(bucketName) {
        if (!this.buckets.has(bucketName)) {
            this.buckets.set(bucketName, new StorageBucket_1.StorageBucket(this.httpClient, bucketName, this.options));
        }
        return this.buckets.get(bucketName);
    }
    /**
     * Create a new bucket
     */
    async createBucket(name, options) {
        try {
            const response = await this.httpClient.post('/api/v1/storage/v1/bucket', {
                name,
                workspaceId: this.options.workspaceId,
                public: options?.public || false,
                allowedMimeTypes: options?.allowedMimeTypes,
                fileSizeLimit: options?.fileSizeLimit
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error
                };
            }
            return {
                data: response.data,
                error: null
            };
        }
        catch (error) {
            const storageError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Bucket creation failed', 0, error);
            return {
                data: null,
                error: storageError
            };
        }
    }
    /**
     * Get bucket information
     */
    async getBucket(name) {
        try {
            const response = await this.httpClient.get(`/api/v1/storage/v1/bucket/${name}`, {
                params: { workspaceId: this.options.workspaceId }
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error
                };
            }
            return {
                data: response.data,
                error: null
            };
        }
        catch (error) {
            const storageError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Failed to get bucket', 0, error);
            return {
                data: null,
                error: storageError
            };
        }
    }
    /**
     * List all buckets
     */
    async listBuckets() {
        try {
            const response = await this.httpClient.get('/api/v1/storage/v1/bucket', {
                params: { workspaceId: this.options.workspaceId }
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error
                };
            }
            return {
                data: response.data || [],
                error: null
            };
        }
        catch (error) {
            const storageError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Failed to list buckets', 0, error);
            return {
                data: null,
                error: storageError
            };
        }
    }
    /**
     * Update bucket settings
     */
    async updateBucket(name, options) {
        try {
            const response = await this.httpClient.put(`/api/v1/storage/v1/bucket/${name}`, {
                workspaceId: this.options.workspaceId,
                ...options
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error
                };
            }
            return {
                data: response.data,
                error: null
            };
        }
        catch (error) {
            const storageError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Bucket update failed', 0, error);
            return {
                data: null,
                error: storageError
            };
        }
    }
    /**
     * Delete a bucket
     */
    async deleteBucket(name, force = false) {
        try {
            const response = await this.httpClient.delete(`/api/v1/storage/v1/bucket/${name}`, {
                params: {
                    workspaceId: this.options.workspaceId,
                    force: force.toString()
                }
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error
                };
            }
            // Remove bucket from cache
            this.buckets.delete(name);
            return {
                data: response.data || { message: 'Bucket deleted successfully' },
                error: null
            };
        }
        catch (error) {
            const storageError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Bucket deletion failed', 0, error);
            return {
                data: null,
                error: storageError
            };
        }
    }
    /**
     * Empty a bucket (delete all files)
     */
    async emptyBucket(name) {
        try {
            const response = await this.httpClient.delete(`/api/v1/storage/v1/bucket/${name}/empty`, {
                params: { workspaceId: this.options.workspaceId }
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error
                };
            }
            return {
                data: response.data || { message: 'Bucket emptied successfully' },
                error: null
            };
        }
        catch (error) {
            const storageError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Failed to empty bucket', 0, error);
            return {
                data: null,
                error: storageError
            };
        }
    }
    /**
     * Get storage usage statistics
     */
    async getUsage() {
        try {
            const response = await this.httpClient.get('/api/v1/storage/v1/usage', {
                params: { workspaceId: this.options.workspaceId }
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error
                };
            }
            return {
                data: response.data,
                error: null
            };
        }
        catch (error) {
            const storageError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Failed to get usage statistics', 0, error);
            return {
                data: null,
                error: storageError
            };
        }
    }
}
exports.StorageClient = StorageClient;
//# sourceMappingURL=StorageClient.js.map