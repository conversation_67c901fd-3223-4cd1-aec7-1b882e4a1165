import { HttpClient } from '../lib/HttpClient';
import { UploadResponse, DownloadResponse, SignedUrlResponse, ListResponse, UploadOptions, DownloadOptions, ListOptions, SignedUrlOptions, StorageClientOptions } from './types';
export declare class StorageBucket {
    private httpClient;
    private bucketName;
    private options;
    constructor(httpClient: HttpClient, bucketName: string, options: StorageClientOptions);
    /**
     * Upload a file to the bucket
     */
    upload(path: string, file: File | Blob | ArrayBuffer | string, options?: UploadOptions): Promise<UploadResponse>;
    /**
     * Download a file from the bucket
     */
    download(path: string, options?: DownloadOptions): Promise<DownloadResponse>;
    /**
     * Delete a file from the bucket
     */
    remove(paths: string | string[]): Promise<{
        data: {
            message: string;
        } | null;
        error: Error | null;
    }>;
    /**
     * List files in the bucket
     */
    list(path?: string, options?: ListOptions): Promise<ListResponse>;
    /**
     * Create a signed URL for a file
     */
    createSignedUrl(path: string, expiresIn: number, options?: Omit<SignedUrlOptions, 'expiresIn'>): Promise<SignedUrlResponse>;
    /**
     * Create a signed upload URL
     */
    createSignedUploadUrl(path: string, options?: UploadOptions): Promise<SignedUrlResponse>;
    /**
     * Get public URL for a file (if bucket is public)
     */
    getPublicUrl(path: string, options?: DownloadOptions): {
        data: {
            publicUrl: string;
        };
    };
    /**
     * Copy a file within the bucket
     */
    copy(fromPath: string, toPath: string): Promise<{
        data: {
            message: string;
        } | null;
        error: Error | null;
    }>;
    /**
     * Move a file within the bucket
     */
    move(fromPath: string, toPath: string): Promise<{
        data: {
            message: string;
        } | null;
        error: Error | null;
    }>;
}
//# sourceMappingURL=StorageBucket.d.ts.map