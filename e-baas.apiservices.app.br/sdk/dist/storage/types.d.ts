export interface FileObject {
    id: string;
    name: string;
    path: string;
    bucketId: string;
    size: number;
    mimeType: string;
    etag: string;
    lastModified: string;
    metadata?: Record<string, any>;
    isPublic: boolean;
    owner?: string;
    createdAt: string;
    updatedAt: string;
}
export interface BucketInfo {
    id: string;
    name: string;
    public: boolean;
    filesCount: number;
    totalSize: number;
    createdAt: string;
    updatedAt: string;
    allowedMimeTypes?: string[];
    fileSizeLimit?: number;
}
export interface UploadResponse {
    data: FileObject | null;
    error: Error | null;
}
export interface DownloadResponse {
    data: Blob | null;
    error: Error | null;
}
export interface SignedUrlResponse {
    data: {
        signedUrl: string;
        token: string;
        expiresAt: string;
    } | null;
    error: Error | null;
}
export interface ListResponse {
    data: FileObject[] | null;
    error: Error | null;
    hasMore?: boolean;
    nextOffset?: string;
}
export interface BucketOptions {
    public?: boolean;
    allowedMimeTypes?: string[];
    fileSizeLimit?: number;
}
export interface UploadOptions {
    cacheControl?: string;
    contentType?: string;
    metadata?: Record<string, any>;
    upsert?: boolean;
}
export interface DownloadOptions {
    transform?: {
        width?: number;
        height?: number;
        quality?: number;
        format?: 'webp' | 'jpeg' | 'png' | 'avif';
        resize?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
    };
}
export interface ListOptions {
    limit?: number;
    offset?: string;
    sortBy?: string[];
    search?: string;
    prefix?: string;
}
export interface SignedUrlOptions {
    expiresIn: number;
    transform?: DownloadOptions['transform'];
}
export interface StorageClientOptions {
    workspaceId: string;
}
//# sourceMappingURL=types.d.ts.map