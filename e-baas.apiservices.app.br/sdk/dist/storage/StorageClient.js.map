{"version": 3, "file": "StorageClient.js", "sourceRoot": "", "sources": ["../../src/storage/StorageClient.ts"], "names": [], "mappings": ";;;AACA,mDAAgD;AAChD,kDAA+C;AAG/C,MAAa,aAAa;IAKxB,YAAY,UAAsB,EAAE,OAA6B;QAFzD,YAAO,GAA+B,IAAI,GAAG,EAAE,CAAC;QAGtD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,UAAkB;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,6BAAa,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC7F,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,IAAY,EAAE,OAAuB;QACtD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAa,2BAA2B,EAAE;gBACnF,IAAI;gBACJ,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;gBACrC,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,KAAK;gBAChC,gBAAgB,EAAE,OAAO,EAAE,gBAAgB;gBAC3C,aAAa,EAAE,OAAO,EAAE,aAAa;aACtC,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACvE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,EACjE,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,IAAY;QAC1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAa,6BAA6B,IAAI,EAAE,EAAE;gBAC1F,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;aAClD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACvE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB,EAC/D,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAe,2BAA2B,EAAE;gBACpF,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;aAClD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE;gBACzB,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACvE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,EACjE,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,IAAY,EAAE,OAAsB;QACrD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAa,6BAA6B,IAAI,EAAE,EAAE;gBAC1F,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;gBACrC,GAAG,OAAO;aACX,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACvE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB,EAC/D,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,IAAY,EAAE,QAAiB,KAAK;QACrD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,6BAA6B,IAAI,EAAE,EAAE;gBACjF,MAAM,EAAE;oBACN,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;oBACrC,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;iBACxB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,2BAA2B;YAC3B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAE1B,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE,OAAO,EAAE,6BAA6B,EAAE;gBACjE,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACvE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,EACjE,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,IAAY;QAC5B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,6BAA6B,IAAI,QAAQ,EAAE;gBACvF,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;aAClD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE,OAAO,EAAE,6BAA6B,EAAE;gBACjE,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACvE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,EACjE,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QASZ,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,0BAA0B,EAAE;gBACrE,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;aAClD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACvE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,gCAAgC,EACzE,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AApRD,sCAoRC"}