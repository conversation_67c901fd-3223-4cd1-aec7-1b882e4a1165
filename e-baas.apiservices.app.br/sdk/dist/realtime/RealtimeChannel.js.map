{"version": 3, "file": "RealtimeChannel.js", "sourceRoot": "", "sources": ["../../src/realtime/RealtimeChannel.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAG7C,MAAa,eAAgB,SAAQ,4BAAY;IAM/C,YAAY,KAAa,EAAE,MAAW;QACpC,KAAK,EAAE,CAAC;QAJF,eAAU,GAAY,KAAK,CAAC;QAC5B,UAAK,GAA4D,QAAQ,CAAC;QAIhF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,iBAAiB,CACf,MAKC,EACD,QAA0C;QAE1C,MAAM,QAAQ,GAAG,oBAAoB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9D,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE7B,OAAO;YACL,WAAW,EAAE,GAAG,EAAE;gBAChB,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAChC,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,WAAW,CACT,MAAyB,EACzB,QAAgC;QAEhC,MAAM,QAAQ,GAAG,aAAa,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QACvD,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE7B,OAAO;YACL,WAAW,EAAE,GAAG,EAAE;gBAChB,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAChC,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,UAAU,CACR,MAA4C,EAC5C,QAAgC;QAEhC,MAAM,QAAQ,GAAG,YAAY,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QACtD,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE7B,OAAO;YACL,WAAW,EAAE,GAAG,EAAE;gBAChB,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAChC,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,OAA4D;QAC/D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;oBAC5B,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,OAAO,EAAE,OAAO;oBAChB,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;iBACpB,CAAC,CAAC;gBACH,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,OAAO,CAAC,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAA4B;QAChC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;oBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,OAAO;oBACd,OAAO,EAAE,OAAO;oBAChB,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;iBACpB,CAAC,CAAC;gBACH,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,OAAO,CAAC,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;oBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,SAAS;oBAChB,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;iBACpB,CAAC,CAAC;gBACH,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,OAAO,CAAC,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,QAAoF;QAC5F,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YAEvB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;oBACxB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;iBACpB,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,MAAM;QACZ,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS;YAAE,OAAO;QAErC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QAEvB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;gBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;aACpB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,OAAO;QACb,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,0CAA0C;IAC1C,QAAQ,CAAC,KAAa,EAAE,OAAY;QAClC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED,SAAS,CAAC,KAAwB;QAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;IAC3C,CAAC;CACF;AA1LD,0CA0LC"}