"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealtimeClient = void 0;
const socket_io_client_1 = require("socket.io-client");
const RealtimeChannel_1 = require("./RealtimeChannel");
class RealtimeClient {
    constructor(url, options) {
        this.socket = null;
        this.channels = new Map();
        this.accessToken = null;
        this.url = url.replace(/^http/, 'ws'); // Convert HTTP to WebSocket URL
        this.options = options;
        this.accessToken = options.apikey;
    }
    /**
     * Set authentication token
     */
    setAuth(token) {
        this.accessToken = token;
        if (this.socket) {
            this.socket.auth = { token };
        }
    }
    /**
     * Connect to the realtime server
     */
    connect() {
        if (this.socket?.connected) {
            return;
        }
        this.socket = (0, socket_io_client_1.io)(this.url, {
            auth: {
                token: this.accessToken,
                workspaceId: this.options.workspaceId
            },
            query: this.options.params,
            transports: ['websocket'],
            autoConnect: true,
            reconnection: true,
            reconnectionDelay: this.options.reconnectDelayMs || 1000,
            timeout: 20000
        });
        this.setupEventHandlers();
    }
    /**
     * Disconnect from the realtime server
     */
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        // Clean up channels
        this.channels.forEach(channel => {
            channel.unsubscribe();
        });
        this.channels.clear();
    }
    /**
     * Get or create a channel
     */
    channel(topic, options) {
        if (!this.channels.has(topic)) {
            const channel = new RealtimeChannel_1.RealtimeChannel(topic, this.socket);
            this.channels.set(topic, channel);
            // Auto-connect if not connected
            if (!this.socket?.connected) {
                this.connect();
            }
        }
        return this.channels.get(topic);
    }
    /**
     * Remove a channel
     */
    removeChannel(channel) {
        channel.unsubscribe();
        this.channels.delete(channel.topic);
    }
    /**
     * Remove all channels
     */
    removeAllChannels() {
        this.channels.forEach(channel => {
            channel.unsubscribe();
        });
        this.channels.clear();
    }
    /**
     * Get connection status
     */
    get isConnected() {
        return this.socket?.connected || false;
    }
    setupEventHandlers() {
        if (!this.socket)
            return;
        this.socket.on('connect', () => {
            if (this.options.logger) {
                this.options.logger('info', 'Connected to realtime server');
            }
        });
        this.socket.on('disconnect', (reason) => {
            if (this.options.logger) {
                this.options.logger('info', 'Disconnected from realtime server', { reason });
            }
        });
        this.socket.on('error', (error) => {
            if (this.options.logger) {
                this.options.logger('error', 'Realtime connection error', { error });
            }
        });
        // Handle channel-specific events
        this.socket.on('postgres_changes', (data) => {
            const { topic, payload } = data;
            const channel = this.channels.get(topic);
            if (channel) {
                channel._trigger(`postgres_changes:${JSON.stringify(payload.filter || {})}`, payload);
            }
        });
        this.socket.on('broadcast', (data) => {
            const { topic, event, payload } = data;
            const channel = this.channels.get(topic);
            if (channel) {
                channel._trigger(`broadcast:${JSON.stringify({ event })}`, payload);
            }
        });
        this.socket.on('presence', (data) => {
            const { topic, event, payload } = data;
            const channel = this.channels.get(topic);
            if (channel) {
                channel._trigger(`presence:${JSON.stringify({ event })}`, payload);
            }
        });
        // Handle channel join/leave responses
        this.socket.on('join_reply', (data) => {
            const { topic, status } = data;
            const channel = this.channels.get(topic);
            if (channel) {
                if (status === 'ok') {
                    channel._setState('joined');
                }
                else {
                    channel._setState('errored');
                }
            }
        });
        this.socket.on('leave_reply', (data) => {
            const { topic } = data;
            const channel = this.channels.get(topic);
            if (channel) {
                channel._setState('closed');
            }
        });
        // Setup heartbeat
        if (this.options.heartbeatIntervalMs) {
            setInterval(() => {
                if (this.socket?.connected) {
                    this.socket.emit('heartbeat', { timestamp: Date.now() });
                }
            }, this.options.heartbeatIntervalMs);
        }
    }
}
exports.RealtimeClient = RealtimeClient;
//# sourceMappingURL=RealtimeClient.js.map