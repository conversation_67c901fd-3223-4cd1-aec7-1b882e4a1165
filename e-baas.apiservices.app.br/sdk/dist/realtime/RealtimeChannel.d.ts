import { EventEmitter } from 'eventemitter3';
import { ChannelSubscription, RealtimeEvent } from './types';
export declare class RealtimeChannel extends EventEmitter {
    topic: string;
    private socket;
    private joinedOnce;
    private state;
    constructor(topic: string, socket: any);
    /**
     * Subscribe to postgres changes
     */
    onPostgresChanges(filter: {
        event: '*' | 'INSERT' | 'UPDATE' | 'DELETE';
        schema: string;
        table?: string;
        filter?: string;
    }, callback: (payload: RealtimeEvent) => void): ChannelSubscription;
    /**
     * Subscribe to broadcast events
     */
    onBroadcast(filter: {
        event: string;
    }, callback: (payload: any) => void): ChannelSubscription;
    /**
     * Subscribe to presence events
     */
    onPresence(filter: {
        event: 'sync' | 'join' | 'leave';
    }, callback: (payload: any) => void): ChannelSubscription;
    /**
     * Send a broadcast message
     */
    send(payload: {
        type: string;
        event: string;
        [key: string]: any;
    }): Promise<'ok' | 'error' | 'timeout'>;
    /**
     * Track presence
     */
    track(payload: Record<string, any>): Promise<'ok' | 'error' | 'timeout'>;
    /**
     * Untrack presence
     */
    untrack(): Promise<'ok' | 'error' | 'timeout'>;
    /**
     * Subscribe to the channel
     */
    subscribe(callback?: (status: 'SUBSCRIBED' | 'CHANNEL_ERROR' | 'TIMED_OUT' | 'CLOSED') => void): RealtimeChannel;
    /**
     * Unsubscribe from the channel
     */
    unsubscribe(): Promise<'ok' | 'error' | 'timeout'>;
    private rejoin;
    private makeRef;
    _trigger(event: string, payload: any): void;
    _setState(state: typeof this.state): void;
}
//# sourceMappingURL=RealtimeChannel.d.ts.map