import { RealtimeChannel } from './RealtimeChannel';
import { RealtimeClientOptions, ChannelOptions } from './types';
export declare class RealtimeClient {
    private socket;
    private channels;
    private url;
    private options;
    private accessToken;
    constructor(url: string, options: RealtimeClientOptions);
    /**
     * Set authentication token
     */
    setAuth(token: string): void;
    /**
     * Connect to the realtime server
     */
    connect(): void;
    /**
     * Disconnect from the realtime server
     */
    disconnect(): void;
    /**
     * Get or create a channel
     */
    channel(topic: string, options?: ChannelOptions): RealtimeChannel;
    /**
     * Remove a channel
     */
    removeChannel(channel: RealtimeChannel): void;
    /**
     * Remove all channels
     */
    removeAllChannels(): void;
    /**
     * Get connection status
     */
    get isConnected(): boolean;
    private setupEventHandlers;
}
//# sourceMappingURL=RealtimeClient.d.ts.map