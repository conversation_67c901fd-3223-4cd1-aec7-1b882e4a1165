"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealtimeChannel = void 0;
const eventemitter3_1 = require("eventemitter3");
class RealtimeChannel extends eventemitter3_1.EventEmitter {
    constructor(topic, socket) {
        super();
        this.joinedOnce = false;
        this.state = 'closed';
        this.topic = topic;
        this.socket = socket;
    }
    /**
     * Subscribe to postgres changes
     */
    onPostgresChanges(filter, callback) {
        const eventKey = `postgres_changes:${JSON.stringify(filter)}`;
        super.on(eventKey, callback);
        return {
            unsubscribe: () => {
                super.off(eventKey, callback);
            }
        };
    }
    /**
     * Subscribe to broadcast events
     */
    onBroadcast(filter, callback) {
        const eventKey = `broadcast:${JSON.stringify(filter)}`;
        super.on(eventKey, callback);
        return {
            unsubscribe: () => {
                super.off(eventKey, callback);
            }
        };
    }
    /**
     * Subscribe to presence events
     */
    onPresence(filter, callback) {
        const eventKey = `presence:${JSON.stringify(filter)}`;
        super.on(eventKey, callback);
        return {
            unsubscribe: () => {
                super.off(eventKey, callback);
            }
        };
    }
    /**
     * Send a broadcast message
     */
    send(payload) {
        return new Promise((resolve) => {
            if (this.socket && this.state === 'joined') {
                this.socket.emit('broadcast', {
                    topic: this.topic,
                    event: payload.event,
                    payload: payload,
                    ref: this.makeRef()
                });
                resolve('ok');
            }
            else {
                resolve('error');
            }
        });
    }
    /**
     * Track presence
     */
    track(payload) {
        return new Promise((resolve) => {
            if (this.socket && this.state === 'joined') {
                this.socket.emit('presence', {
                    topic: this.topic,
                    event: 'track',
                    payload: payload,
                    ref: this.makeRef()
                });
                resolve('ok');
            }
            else {
                resolve('error');
            }
        });
    }
    /**
     * Untrack presence
     */
    untrack() {
        return new Promise((resolve) => {
            if (this.socket && this.state === 'joined') {
                this.socket.emit('presence', {
                    topic: this.topic,
                    event: 'untrack',
                    ref: this.makeRef()
                });
                resolve('ok');
            }
            else {
                resolve('error');
            }
        });
    }
    /**
     * Subscribe to the channel
     */
    subscribe(callback) {
        if (!this.joinedOnce) {
            this.joinedOnce = true;
            this.rejoin();
        }
        if (callback) {
            super.on('status', callback);
        }
        return this;
    }
    /**
     * Unsubscribe from the channel
     */
    unsubscribe() {
        return new Promise((resolve) => {
            this.state = 'leaving';
            if (this.socket) {
                this.socket.emit('leave', {
                    topic: this.topic,
                    ref: this.makeRef()
                });
            }
            this.state = 'closed';
            this.removeAllListeners();
            resolve('ok');
        });
    }
    rejoin() {
        if (this.state === 'leaving')
            return;
        this.state = 'joining';
        if (this.socket) {
            this.socket.emit('join', {
                topic: this.topic,
                ref: this.makeRef()
            });
        }
    }
    makeRef() {
        return Math.random().toString(36).substring(2, 15);
    }
    // Internal methods for socket integration
    _trigger(event, payload) {
        this.emit(event, payload);
    }
    _setState(state) {
        this.state = state;
        this.emit('status', state.toUpperCase());
    }
}
exports.RealtimeChannel = RealtimeChannel;
//# sourceMappingURL=RealtimeChannel.js.map