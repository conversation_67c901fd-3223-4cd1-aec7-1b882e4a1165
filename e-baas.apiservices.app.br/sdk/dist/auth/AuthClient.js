"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthClient = void 0;
const EBaaSError_1 = require("../lib/EBaaSError");
const STORAGE_KEY = 'ebaas-auth-token';
class AuthClient {
    constructor(httpClient, options = {}) {
        this.currentSession = null;
        this.currentUser = null;
        this.authChangeCallbacks = new Set();
        this.refreshTokenTimeout = null;
        this.httpClient = httpClient;
        this.options = {
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: true,
            flowType: 'implicit',
            debug: false,
            ...options
        };
        // Initialize session from storage
        if (this.options.persistSession && typeof window !== 'undefined') {
            this.initializeSession();
        }
        // Detect session in URL (for OAuth callbacks)
        if (this.options.detectSessionInUrl && typeof window !== 'undefined') {
            this.detectSessionInUrl();
        }
    }
    /**
     * Get current user
     */
    user() {
        return this.currentUser;
    }
    /**
     * Get current session
     */
    session() {
        return this.currentSession;
    }
    /**
     * Sign up with email and password
     */
    async signUp(data) {
        try {
            const response = await this.httpClient.post('/api/v1/auth/signup', data);
            if (response.error) {
                return {
                    data: { user: null, session: null },
                    error: response.error
                };
            }
            if (response.data) {
                await this.setSession(response.data.session);
                this.emitAuthChange('SIGNED_IN', response.data.session);
            }
            return {
                data: {
                    user: response.data?.user || null,
                    session: response.data?.session || null
                },
                error: null
            };
        }
        catch (error) {
            const authError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Sign up failed', 0, error);
            return {
                data: { user: null, session: null },
                error: authError
            };
        }
    }
    /**
     * Sign in with email and password
     */
    async signInWithPassword(data) {
        try {
            const response = await this.httpClient.post('/api/v1/auth/signin', data);
            if (response.error) {
                return {
                    data: { user: null, session: null },
                    error: response.error
                };
            }
            if (response.data) {
                await this.setSession(response.data.session);
                this.emitAuthChange('SIGNED_IN', response.data.session);
            }
            return {
                data: {
                    user: response.data?.user || null,
                    session: response.data?.session || null
                },
                error: null
            };
        }
        catch (error) {
            const authError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Sign in failed', 0, error);
            return {
                data: { user: null, session: null },
                error: authError
            };
        }
    }
    /**
     * Sign in with OAuth provider
     */
    async signInWithOAuth(data) {
        try {
            const response = await this.httpClient.post(`/api/v1/auth/oauth/${data.provider}`, {
                redirectTo: data.redirectTo,
                scopes: data.scopes
            });
            if (response.error) {
                return {
                    data: { user: null, session: null },
                    error: response.error
                };
            }
            if (response.data?.url && typeof window !== 'undefined') {
                // Redirect to OAuth provider
                window.location.href = response.data.url;
            }
            return {
                data: { user: null, session: null },
                error: null
            };
        }
        catch (error) {
            const authError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'OAuth sign in failed', 0, error);
            return {
                data: { user: null, session: null },
                error: authError
            };
        }
    }
    /**
     * Sign out
     */
    async signOut() {
        try {
            await this.httpClient.post('/api/v1/auth/signout');
            await this.removeSession();
            this.emitAuthChange('SIGNED_OUT', null);
            return { error: null };
        }
        catch (error) {
            const authError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Sign out failed', 0, error);
            return { error: authError };
        }
    }
    /**
     * Refresh the current session
     */
    async refreshSession() {
        try {
            const refreshToken = this.currentSession?.refresh_token;
            if (!refreshToken) {
                throw new EBaaSError_1.EBaaSError('No refresh token available', 401);
            }
            const response = await this.httpClient.post('/api/v1/auth/refresh', {
                refresh_token: refreshToken
            });
            if (response.error) {
                await this.removeSession();
                return {
                    data: { user: null, session: null },
                    error: response.error
                };
            }
            if (response.data) {
                await this.setSession(response.data.session);
                this.emitAuthChange('TOKEN_REFRESHED', response.data.session);
            }
            return {
                data: {
                    user: response.data?.user || null,
                    session: response.data?.session || null
                },
                error: null
            };
        }
        catch (error) {
            const authError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Token refresh failed', 0, error);
            await this.removeSession();
            return {
                data: { user: null, session: null },
                error: authError
            };
        }
    }
    /**
     * Update user information
     */
    async updateUser(data) {
        try {
            const response = await this.httpClient.put('/api/v1/auth/user', data);
            if (response.error) {
                return {
                    data: { user: this.currentUser, session: this.currentSession },
                    error: response.error
                };
            }
            if (response.data?.user) {
                this.currentUser = response.data.user;
                this.emitAuthChange('USER_UPDATED', this.currentSession);
            }
            return {
                data: {
                    user: response.data?.user || this.currentUser,
                    session: this.currentSession
                },
                error: null
            };
        }
        catch (error) {
            const authError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'User update failed', 0, error);
            return {
                data: { user: this.currentUser, session: this.currentSession },
                error: authError
            };
        }
    }
    /**
     * Reset password
     */
    async resetPasswordForEmail(data) {
        try {
            const response = await this.httpClient.post('/api/v1/auth/reset-password', data);
            if (response.error) {
                return { error: response.error };
            }
            return { error: null };
        }
        catch (error) {
            const authError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Password reset failed', 0, error);
            return { error: authError };
        }
    }
    /**
     * Listen to auth state changes
     */
    onAuthStateChange(callback) {
        this.authChangeCallbacks.add(callback);
        // Immediately call with current state
        if (this.currentSession) {
            callback('SIGNED_IN', this.currentSession);
        }
        return {
            data: {
                subscription: {
                    unsubscribe: () => {
                        this.authChangeCallbacks.delete(callback);
                    }
                }
            }
        };
    }
    async setSession(session) {
        this.currentSession = session;
        this.currentUser = session.user;
        // Update HTTP client auth header
        this.httpClient.setAuthToken(session.access_token);
        // Persist session
        if (this.options.persistSession && typeof window !== 'undefined') {
            localStorage.setItem(STORAGE_KEY, JSON.stringify(session));
        }
        // Setup auto refresh
        if (this.options.autoRefreshToken) {
            this.scheduleTokenRefresh(session.expires_in);
        }
    }
    async removeSession() {
        this.currentSession = null;
        this.currentUser = null;
        // Clear auth from HTTP client
        this.httpClient.removeAuthToken();
        // Remove from storage
        if (typeof window !== 'undefined') {
            localStorage.removeItem(STORAGE_KEY);
        }
        // Clear refresh timeout
        if (this.refreshTokenTimeout) {
            clearTimeout(this.refreshTokenTimeout);
            this.refreshTokenTimeout = null;
        }
    }
    initializeSession() {
        try {
            const storedSession = localStorage.getItem(STORAGE_KEY);
            if (storedSession) {
                const session = JSON.parse(storedSession);
                // Check if session is still valid
                if (session.expires_at && session.expires_at > Date.now()) {
                    this.setSession(session);
                }
                else {
                    localStorage.removeItem(STORAGE_KEY);
                }
            }
        }
        catch (error) {
            if (this.options.debug) {
                console.error('Failed to initialize session:', error);
            }
        }
    }
    detectSessionInUrl() {
        const params = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = params.get('access_token');
        const refreshToken = params.get('refresh_token');
        const expiresIn = params.get('expires_in');
        if (accessToken && refreshToken && expiresIn) {
            // Clear URL fragments
            window.history.replaceState({}, document.title, window.location.pathname + window.location.search);
            // Create session from URL params
            const session = {
                access_token: accessToken,
                refresh_token: refreshToken,
                expires_in: parseInt(expiresIn),
                expires_at: Date.now() + (parseInt(expiresIn) * 1000),
                token_type: 'bearer',
                user: {} // Will be populated by getUser call
            };
            this.setSession(session);
            this.getUser(); // Fetch user data
        }
    }
    async getUser() {
        try {
            const response = await this.httpClient.get('/api/v1/auth/user');
            if (response.data?.user) {
                this.currentUser = response.data.user;
                if (this.currentSession) {
                    this.currentSession.user = response.data.user;
                }
            }
        }
        catch (error) {
            if (this.options.debug) {
                console.error('Failed to fetch user:', error);
            }
        }
    }
    scheduleTokenRefresh(expiresIn) {
        if (this.refreshTokenTimeout) {
            clearTimeout(this.refreshTokenTimeout);
        }
        // Refresh token 5 minutes before expiry
        const refreshTime = Math.max(0, (expiresIn - 300) * 1000);
        this.refreshTokenTimeout = setTimeout(() => {
            this.refreshSession();
        }, refreshTime);
    }
    emitAuthChange(event, session) {
        this.authChangeCallbacks.forEach(callback => {
            try {
                callback(event, session);
            }
            catch (error) {
                if (this.options.debug) {
                    console.error('Auth change callback error:', error);
                }
            }
        });
    }
}
exports.AuthClient = AuthClient;
//# sourceMappingURL=AuthClient.js.map