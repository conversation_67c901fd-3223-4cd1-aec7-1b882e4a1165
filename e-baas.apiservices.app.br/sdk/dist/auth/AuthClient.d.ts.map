{"version": 3, "file": "AuthClient.d.ts", "sourceRoot": "", "sources": ["../../src/auth/AuthClient.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,OAAO,EACL,IAAI,EACJ,OAAO,EACP,YAAY,EACZ,UAAU,EACV,UAAU,EACV,eAAe,EACf,iBAAiB,EACjB,cAAc,EAGd,kBAAkB,EAClB,iBAAiB,EAClB,MAAM,SAAS,CAAC;AAIjB,qBAAa,UAAU;IACrB,OAAO,CAAC,UAAU,CAAa;IAC/B,OAAO,CAAC,OAAO,CAAoB;IACnC,OAAO,CAAC,cAAc,CAAwB;IAC9C,OAAO,CAAC,WAAW,CAAqB;IACxC,OAAO,CAAC,mBAAmB,CAAsC;IACjE,OAAO,CAAC,mBAAmB,CAA+B;gBAE9C,UAAU,EAAE,UAAU,EAAE,OAAO,GAAE,iBAAsB;IAsBnE;;OAEG;IACH,IAAI,IAAI,IAAI,GAAG,IAAI;IAInB;;OAEG;IACH,OAAO,IAAI,OAAO,GAAG,IAAI;IAIzB;;OAEG;IACG,MAAM,CAAC,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC;IAqCrD;;OAEG;IACG,kBAAkB,CAAC,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC;IAqCjE;;OAEG;IACG,eAAe,CAAC,IAAI,EAAE,eAAe,GAAG,OAAO,CAAC,YAAY,CAAC;IAqCnE;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC;QAAE,KAAK,EAAE,KAAK,GAAG,IAAI,CAAA;KAAE,CAAC;IAmBjD;;OAEG;IACG,cAAc,IAAI,OAAO,CAAC,YAAY,CAAC;IA8C7C;;OAEG;IACG,UAAU,CAAC,IAAI,EAAE,cAAc,GAAG,OAAO,CAAC,YAAY,CAAC;IAqC7D;;OAEG;IACG,qBAAqB,CAAC,IAAI,EAAE,iBAAiB,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,KAAK,GAAG,IAAI,CAAA;KAAE,CAAC;IAoBtF;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,kBAAkB,GAAG;QAAE,IAAI,EAAE;YAAE,YAAY,EAAE;gBAAE,WAAW,EAAE,MAAM,IAAI,CAAA;aAAE,CAAA;SAAE,CAAA;KAAE;YAmB1F,UAAU;YAkBV,aAAa;IAmB3B,OAAO,CAAC,iBAAiB;IAoBzB,OAAO,CAAC,kBAAkB;YAyBZ,OAAO;IAgBrB,OAAO,CAAC,oBAAoB;IAa5B,OAAO,CAAC,cAAc;CAWvB"}