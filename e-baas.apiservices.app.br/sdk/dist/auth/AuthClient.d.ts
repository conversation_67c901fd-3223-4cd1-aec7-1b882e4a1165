import { HttpClient } from '../lib/HttpClient';
import { User, Session, AuthResponse, SignUpData, SignInData, OAuthSignInData, ResetPasswordData, UpdateUserData, AuthChangeCallback, AuthClientOptions } from './types';
export declare class AuthClient {
    private httpClient;
    private options;
    private currentSession;
    private currentUser;
    private authChangeCallbacks;
    private refreshTokenTimeout;
    constructor(httpClient: HttpClient, options?: AuthClientOptions);
    /**
     * Get current user
     */
    user(): User | null;
    /**
     * Get current session
     */
    session(): Session | null;
    /**
     * Sign up with email and password
     */
    signUp(data: SignUpData): Promise<AuthResponse>;
    /**
     * Sign in with email and password
     */
    signInWithPassword(data: SignInData): Promise<AuthResponse>;
    /**
     * Sign in with OAuth provider
     */
    signInWithOAuth(data: OAuthSignInData): Promise<AuthResponse>;
    /**
     * Sign out
     */
    signOut(): Promise<{
        error: Error | null;
    }>;
    /**
     * Refresh the current session
     */
    refreshSession(): Promise<AuthResponse>;
    /**
     * Update user information
     */
    updateUser(data: UpdateUserData): Promise<AuthResponse>;
    /**
     * Reset password
     */
    resetPasswordForEmail(data: ResetPasswordData): Promise<{
        error: Error | null;
    }>;
    /**
     * Listen to auth state changes
     */
    onAuthStateChange(callback: AuthChangeCallback): {
        data: {
            subscription: {
                unsubscribe: () => void;
            };
        };
    };
    private setSession;
    private removeSession;
    private initializeSession;
    private detectSessionInUrl;
    private getUser;
    private scheduleTokenRefresh;
    private emitAuthChange;
}
//# sourceMappingURL=AuthClient.d.ts.map