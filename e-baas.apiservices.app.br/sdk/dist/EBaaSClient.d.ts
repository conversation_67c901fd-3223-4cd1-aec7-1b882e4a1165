import { AuthClient } from './auth/AuthClient';
import { DatabaseClient } from './database/DatabaseClient';
import { StorageClient } from './storage/StorageClient';
import { RealtimeClient } from './realtime/RealtimeClient';
import { EdgeFunctionsClient } from './edge-functions/EdgeFunctionsClient';
export interface EBaaSClientOptions {
    url: string;
    apikey: string;
    workspaceId: string;
    schema?: string;
    headers?: Record<string, string>;
    autoRefreshToken?: boolean;
    persistSession?: boolean;
    detectSessionInUrl?: boolean;
    realtime?: {
        params?: Record<string, string>;
        heartbeatIntervalMs?: number;
        reconnectDelayMs?: number;
    };
}
/**
 * Main E-BaaS client class - entry point for all E-BaaS functionality
 * Similar to Supabase's createClient()
 */
export declare class EBaaSClient {
    readonly supabaseUrl: string;
    readonly supabaseKey: string;
    readonly workspaceId: string;
    readonly auth: AuthClient;
    readonly database: DatabaseClient;
    readonly storage: StorageClient;
    readonly realtime: RealtimeClient;
    readonly functions: EdgeFunctionsClient;
    private httpClient;
    constructor(options: EBaaSClientOptions);
    /**
     * Create a table query builder
     * Usage: client.from('users').select('*')
     */
    from(table: string): import(".").QueryBuilder<any>;
    /**
     * Execute raw SQL query
     * Usage: client.rpc('my_function', { param1: 'value' })
     */
    rpc(fn: string, params?: Record<string, any>): Promise<import("./database/types").RpcResponse<any>>;
    /**
     * Remove all subscriptions and close connections
     */
    removeAllChannels(): void;
    /**
     * Get a realtime channel
     * Usage: client.channel('public:users')
     */
    channel(name: string, options?: any): import(".").RealtimeChannel;
    /**
     * Remove a realtime channel
     */
    removeChannel(channel: any): void;
}
/**
 * Create a new E-BaaS client instance
 * @param url - E-BaaS API URL
 * @param apikey - API key for authentication
 * @param options - Additional client options
 */
export declare function createClient(url: string, apikey: string, options?: Partial<EBaaSClientOptions>): EBaaSClient;
//# sourceMappingURL=EBaaSClient.d.ts.map