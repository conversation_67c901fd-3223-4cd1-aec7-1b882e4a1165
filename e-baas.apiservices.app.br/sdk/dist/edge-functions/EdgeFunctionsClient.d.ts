import { HttpClient } from '../lib/HttpClient';
import { FunctionInvokeOptions, FunctionResponse, EdgeFunctionMeta, EdgeFunctionsClientOptions } from './types';
export declare class EdgeFunctionsClient {
    private httpClient;
    private options;
    constructor(httpClient: HttpClient, options: EdgeFunctionsClientOptions);
    /**
     * Invoke an edge function
     */
    invoke<T = any>(functionName: string, options?: FunctionInvokeOptions): Promise<FunctionResponse<T>>;
    /**
     * List available edge functions
     */
    list(): Promise<{
        data: EdgeFunctionMeta[] | null;
        error: Error | null;
    }>;
    /**
     * Get edge function metadata
     */
    get(functionName: string): Promise<{
        data: EdgeFunctionMeta | null;
        error: Error | null;
    }>;
    /**
     * Deploy an edge function
     */
    deploy(functionName: string, code: string, options?: {
        description?: string;
        runtime?: string;
        env?: Record<string, string>;
    }): Promise<{
        data: EdgeFunctionMeta | null;
        error: Error | null;
    }>;
    /**
     * Delete an edge function
     */
    delete(functionName: string): Promise<{
        data: {
            message: string;
        } | null;
        error: Error | null;
    }>;
}
//# sourceMappingURL=EdgeFunctionsClient.d.ts.map