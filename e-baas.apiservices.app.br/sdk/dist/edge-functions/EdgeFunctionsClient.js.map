{"version": 3, "file": "EdgeFunctionsClient.js", "sourceRoot": "", "sources": ["../../src/edge-functions/EdgeFunctionsClient.ts"], "names": [], "mappings": ";;;AACA,kDAA+C;AAQ/C,MAAa,mBAAmB;IAI9B,YAAY,UAAsB,EAAE,OAAmC;QACrE,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACV,YAAoB,EACpB,OAA+B;QAE/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,IAAI,MAAM,CAAC;YACzC,MAAM,OAAO,GAAG;gBACd,cAAc,EAAE,kBAAkB;gBAClC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;gBAC1C,GAAG,OAAO,EAAE,OAAO;aACpB,CAAC;YAEF,MAAM,MAAM,GAAG;gBACb,OAAO;gBACP,GAAG,CAAC,OAAO,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC;aAC7C,CAAC;YAEF,IAAI,QAAQ,CAAC;YACb,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,KAAK;oBACR,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAI,qBAAqB,YAAY,EAAE,EAAE,MAAM,CAAC,CAAC;oBACrF,MAAM;gBACR,KAAK,MAAM;oBACT,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAI,qBAAqB,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;oBACrG,MAAM;gBACR,KAAK,KAAK;oBACR,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAI,qBAAqB,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;oBACpG,MAAM;gBACR,KAAK,OAAO;oBACV,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAI,qBAAqB,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;oBACtG,MAAM;gBACR,KAAK,QAAQ;oBACX,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAI,qBAAqB,YAAY,EAAE,EAAE,MAAM,CAAC,CAAC;oBACxF,MAAM;gBACR;oBACE,MAAM,IAAI,uBAAU,CAAC,4BAA4B,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;iBAChC,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;aAChC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACxE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B,EACrE,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,aAAa;gBACpB,MAAM,EAAE,aAAa,CAAC,MAAM;gBAC5B,UAAU,EAAE,aAAa,CAAC,OAAO;aAClC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAqB,mBAAmB,EAAE;gBAClF,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;aAClD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE;gBACzB,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACxE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B,EACnE,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,aAAa;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CAAC,YAAoB;QAC5B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAmB,qBAAqB,YAAY,OAAO,EAAE;gBACrG,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;aAClD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACxE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,iCAAiC,EAC1E,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,aAAa;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACV,YAAoB,EACpB,IAAY,EACZ,OAIC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAmB,mBAAmB,EAAE;gBACjF,IAAI,EAAE,YAAY;gBAClB,IAAI;gBACJ,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;gBACrC,WAAW,EAAE,OAAO,EAAE,WAAW;gBACjC,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,MAAM;gBACnC,GAAG,EAAE,OAAO,EAAE,GAAG;aAClB,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACxE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B,EACrE,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,aAAa;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,YAAoB;QAC/B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,YAAY,EAAE,EAAE;gBACjF,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;aAClD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE,OAAO,EAAE,+BAA+B,EAAE;gBACnE,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,KAAK,YAAY,uBAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,uBAAU,CACxE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B,EACnE,CAAC,EACD,KAAK,CACN,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,aAAa;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AArOD,kDAqOC"}