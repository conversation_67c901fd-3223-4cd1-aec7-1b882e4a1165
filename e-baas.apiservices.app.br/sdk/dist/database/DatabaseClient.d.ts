import { HttpClient } from '../lib/HttpClient';
import { QueryBuilder } from './QueryBuilder';
import { RpcResponse, DatabaseOptions } from './types';
export declare class DatabaseClient {
    private httpClient;
    private options;
    constructor(httpClient: HttpClient, options: DatabaseOptions);
    /**
     * Create a query builder for a table
     * Usage: db.from('users').select('*').eq('id', 1)
     */
    from<T = any>(table: string): QueryBuilder<T>;
    /**
     * Execute a remote procedure call (RPC)
     * Usage: db.rpc('my_function', { param1: 'value' })
     */
    rpc<T = any>(functionName: string, params?: Record<string, any>): Promise<RpcResponse<T>>;
    /**
     * Execute raw SQL query
     */
    query<T = any>(sql: string, params?: any[]): Promise<RpcResponse<T[]>>;
    /**
     * Get table schema information
     */
    getSchema(tableName?: string): Promise<import("..").EBaaSResponse<any>>;
    /**
     * Create a new table
     */
    createTable(tableName: string, columns: Record<string, any>): Promise<import("..").EBaaSResponse<any>>;
    /**
     * Drop a table
     */
    dropTable(tableName: string): Promise<import("..").EBaaSResponse<any>>;
}
//# sourceMappingURL=DatabaseClient.d.ts.map