import { HttpClient } from '../lib/HttpClient';
import { QueryResponse, InsertResponse, UpdateResponse, DeleteResponse, QueryOptions } from './types';
export declare class QueryBuilder<T = any> {
    private httpClient;
    private tableName;
    private workspaceId;
    private schema;
    private selectFields;
    private filters;
    private orderBy;
    private limitValue?;
    private offsetValue?;
    private rangeStart?;
    private rangeEnd?;
    constructor(httpClient: HttpClient, tableName: string, workspaceId: string, schema?: string);
    /**
     * Select specific columns
     * @param columns - Columns to select ('*' for all, or comma-separated list)
     */
    select(columns?: string): QueryBuilder<T>;
    /**
     * Filter rows based on column value
     */
    eq(column: string, value: any): QueryBuilder<T>;
    neq(column: string, value: any): QueryBuilder<T>;
    gt(column: string, value: any): QueryBuilder<T>;
    gte(column: string, value: any): QueryBuilder<T>;
    lt(column: string, value: any): QueryBuilder<T>;
    lte(column: string, value: any): QueryBuilder<T>;
    like(column: string, pattern: string): QueryBuilder<T>;
    ilike(column: string, pattern: string): QueryBuilder<T>;
    is(column: string, value: any): QueryBuilder<T>;
    in(column: string, values: any[]): QueryBuilder<T>;
    contains(column: string, value: any): QueryBuilder<T>;
    /**
     * Order results
     */
    order(column: string, options?: {
        ascending?: boolean;
        nullsFirst?: boolean;
    }): QueryBuilder<T>;
    /**
     * Limit number of results
     */
    limit(count: number): QueryBuilder<T>;
    /**
     * Skip number of results
     */
    offset(count: number): QueryBuilder<T>;
    /**
     * Limit to a range of rows
     */
    range(from: number, to: number): QueryBuilder<T>;
    /**
     * Execute the query and return results
     */
    execute(options?: QueryOptions): Promise<QueryResponse<T>>;
    /**
     * Insert new rows
     */
    insert(data: Partial<T> | Partial<T>[]): Promise<InsertResponse<T>>;
    /**
     * Update existing rows
     */
    update(data: Partial<T>): Promise<UpdateResponse<T>>;
    /**
     * Delete rows
     */
    delete(): Promise<DeleteResponse>;
    private buildQueryParams;
}
//# sourceMappingURL=QueryBuilder.d.ts.map