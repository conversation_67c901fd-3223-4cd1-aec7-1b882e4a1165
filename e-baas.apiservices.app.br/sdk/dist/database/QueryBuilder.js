"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryBuilder = void 0;
const EBaaSError_1 = require("../lib/EBaaSError");
class QueryBuilder {
    constructor(httpClient, tableName, workspaceId, schema = 'public') {
        // Query building state
        this.selectFields = ['*'];
        this.filters = [];
        this.orderBy = [];
        this.httpClient = httpClient;
        this.tableName = tableName;
        this.workspaceId = workspaceId;
        this.schema = schema;
    }
    /**
     * Select specific columns
     * @param columns - Columns to select ('*' for all, or comma-separated list)
     */
    select(columns = '*') {
        this.selectFields = columns === '*' ? ['*'] : columns.split(',').map(c => c.trim());
        return this;
    }
    /**
     * Filter rows based on column value
     */
    eq(column, value) {
        this.filters.push({ column, operator: 'eq', value });
        return this;
    }
    neq(column, value) {
        this.filters.push({ column, operator: 'neq', value });
        return this;
    }
    gt(column, value) {
        this.filters.push({ column, operator: 'gt', value });
        return this;
    }
    gte(column, value) {
        this.filters.push({ column, operator: 'gte', value });
        return this;
    }
    lt(column, value) {
        this.filters.push({ column, operator: 'lt', value });
        return this;
    }
    lte(column, value) {
        this.filters.push({ column, operator: 'lte', value });
        return this;
    }
    like(column, pattern) {
        this.filters.push({ column, operator: 'like', value: pattern });
        return this;
    }
    ilike(column, pattern) {
        this.filters.push({ column, operator: 'ilike', value: pattern });
        return this;
    }
    is(column, value) {
        this.filters.push({ column, operator: 'is', value });
        return this;
    }
    in(column, values) {
        this.filters.push({ column, operator: 'in', value: values });
        return this;
    }
    contains(column, value) {
        this.filters.push({ column, operator: 'contains', value });
        return this;
    }
    /**
     * Order results
     */
    order(column, options) {
        this.orderBy.push({
            column,
            ascending: options?.ascending !== false,
            nullsFirst: options?.nullsFirst
        });
        return this;
    }
    /**
     * Limit number of results
     */
    limit(count) {
        this.limitValue = count;
        return this;
    }
    /**
     * Skip number of results
     */
    offset(count) {
        this.offsetValue = count;
        return this;
    }
    /**
     * Limit to a range of rows
     */
    range(from, to) {
        this.rangeStart = from;
        this.rangeEnd = to;
        return this;
    }
    /**
     * Execute the query and return results
     */
    async execute(options) {
        try {
            const queryParams = this.buildQueryParams(options);
            const endpoint = `/api/v1/database/${this.schema}/${this.tableName}`;
            const response = await this.httpClient.get(endpoint, {
                params: queryParams
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error,
                    status: response.status,
                    statusText: response.statusText
                };
            }
            return {
                data: response.data,
                error: null,
                status: response.status,
                statusText: response.statusText,
                count: Array.isArray(response.data) ? response.data.length : 0
            };
        }
        catch (error) {
            const ebaaError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Unknown error', 0, error);
            return {
                data: null,
                error: ebaaError,
                status: ebaaError.status,
                statusText: ebaaError.message
            };
        }
    }
    /**
     * Insert new rows
     */
    async insert(data) {
        try {
            const endpoint = `/api/v1/database/${this.schema}/${this.tableName}`;
            const response = await this.httpClient.post(endpoint, Array.isArray(data) ? data : [data]);
            if (response.error) {
                return {
                    data: null,
                    error: response.error,
                    status: response.status,
                    statusText: response.statusText
                };
            }
            return {
                data: response.data,
                error: null,
                status: response.status,
                statusText: response.statusText
            };
        }
        catch (error) {
            const ebaaError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Unknown error', 0, error);
            return {
                data: null,
                error: ebaaError,
                status: ebaaError.status,
                statusText: ebaaError.message
            };
        }
    }
    /**
     * Update existing rows
     */
    async update(data) {
        try {
            const queryParams = this.buildQueryParams();
            const endpoint = `/api/v1/database/${this.schema}/${this.tableName}`;
            const response = await this.httpClient.put(endpoint, data, {
                params: queryParams
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error,
                    status: response.status,
                    statusText: response.statusText
                };
            }
            return {
                data: response.data,
                error: null,
                status: response.status,
                statusText: response.statusText
            };
        }
        catch (error) {
            const ebaaError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Unknown error', 0, error);
            return {
                data: null,
                error: ebaaError,
                status: ebaaError.status,
                statusText: ebaaError.message
            };
        }
    }
    /**
     * Delete rows
     */
    async delete() {
        try {
            const queryParams = this.buildQueryParams();
            const endpoint = `/api/v1/database/${this.schema}/${this.tableName}`;
            const response = await this.httpClient.delete(endpoint, {
                params: queryParams
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error,
                    status: response.status,
                    statusText: response.statusText
                };
            }
            return {
                data: null,
                error: null,
                status: response.status,
                statusText: response.statusText
            };
        }
        catch (error) {
            const ebaaError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Unknown error', 0, error);
            return {
                data: null,
                error: ebaaError,
                status: ebaaError.status,
                statusText: ebaaError.message
            };
        }
    }
    buildQueryParams(options) {
        const params = {
            workspaceId: this.workspaceId
        };
        // Select fields
        if (this.selectFields.length > 0 && !this.selectFields.includes('*')) {
            params.select = this.selectFields.join(',');
        }
        // Filters
        this.filters.forEach((filter, index) => {
            params[`filter[${index}][column]`] = filter.column;
            params[`filter[${index}][operator]`] = filter.operator;
            params[`filter[${index}][value]`] = JSON.stringify(filter.value);
        });
        // Order by
        this.orderBy.forEach((order, index) => {
            params[`order[${index}][column]`] = order.column;
            params[`order[${index}][direction]`] = order.ascending ? 'asc' : 'desc';
            if (order.nullsFirst !== undefined) {
                params[`order[${index}][nullsFirst]`] = order.nullsFirst;
            }
        });
        // Limit and offset
        if (this.limitValue !== undefined) {
            params.limit = this.limitValue;
        }
        if (this.offsetValue !== undefined) {
            params.offset = this.offsetValue;
        }
        // Range
        if (this.rangeStart !== undefined && this.rangeEnd !== undefined) {
            params.rangeStart = this.rangeStart;
            params.rangeEnd = this.rangeEnd;
        }
        // Options
        if (options?.head) {
            params.head = true;
        }
        if (options?.count) {
            params.count = options.count;
        }
        return params;
    }
}
exports.QueryBuilder = QueryBuilder;
//# sourceMappingURL=QueryBuilder.js.map