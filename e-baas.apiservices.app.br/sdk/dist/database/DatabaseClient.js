"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseClient = void 0;
const QueryBuilder_1 = require("./QueryBuilder");
const EBaaSError_1 = require("../lib/EBaaSError");
class DatabaseClient {
    constructor(httpClient, options) {
        this.httpClient = httpClient;
        this.options = options;
    }
    /**
     * Create a query builder for a table
     * Usage: db.from('users').select('*').eq('id', 1)
     */
    from(table) {
        return new QueryBuilder_1.QueryBuilder(this.httpClient, table, this.options.workspaceId, this.options.schema);
    }
    /**
     * Execute a remote procedure call (RPC)
     * Usage: db.rpc('my_function', { param1: 'value' })
     */
    async rpc(functionName, params) {
        try {
            const endpoint = `/api/v1/database/rpc/${functionName}`;
            const response = await this.httpClient.post(endpoint, {
                ...params,
                workspaceId: this.options.workspaceId,
                schema: this.options.schema
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error,
                    status: response.status,
                    statusText: response.statusText
                };
            }
            return {
                data: response.data,
                error: null,
                status: response.status,
                statusText: response.statusText
            };
        }
        catch (error) {
            const ebaaError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Unknown error', 0, error);
            return {
                data: null,
                error: ebaaError,
                status: ebaaError.status,
                statusText: ebaaError.message
            };
        }
    }
    /**
     * Execute raw SQL query
     */
    async query(sql, params) {
        try {
            const endpoint = `/api/v1/database/query`;
            const response = await this.httpClient.post(endpoint, {
                sql,
                params,
                workspaceId: this.options.workspaceId,
                schema: this.options.schema
            });
            if (response.error) {
                return {
                    data: null,
                    error: response.error,
                    status: response.status,
                    statusText: response.statusText
                };
            }
            return {
                data: response.data,
                error: null,
                status: response.status,
                statusText: response.statusText
            };
        }
        catch (error) {
            const ebaaError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Unknown error', 0, error);
            return {
                data: null,
                error: ebaaError,
                status: ebaaError.status,
                statusText: ebaaError.message
            };
        }
    }
    /**
     * Get table schema information
     */
    async getSchema(tableName) {
        try {
            const endpoint = tableName
                ? `/api/v1/database/schema/${this.options.schema}/${tableName}`
                : `/api/v1/database/schema/${this.options.schema}`;
            const response = await this.httpClient.get(endpoint, {
                params: { workspaceId: this.options.workspaceId }
            });
            return response;
        }
        catch (error) {
            const ebaaError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Unknown error', 0, error);
            return {
                data: null,
                error: ebaaError,
                status: ebaaError.status,
                statusText: ebaaError.message
            };
        }
    }
    /**
     * Create a new table
     */
    async createTable(tableName, columns) {
        try {
            const endpoint = `/api/v1/database/schema/${this.options.schema}/tables`;
            const response = await this.httpClient.post(endpoint, {
                tableName,
                columns,
                workspaceId: this.options.workspaceId
            });
            return response;
        }
        catch (error) {
            const ebaaError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Unknown error', 0, error);
            return {
                data: null,
                error: ebaaError,
                status: ebaaError.status,
                statusText: ebaaError.message
            };
        }
    }
    /**
     * Drop a table
     */
    async dropTable(tableName) {
        try {
            const endpoint = `/api/v1/database/schema/${this.options.schema}/tables/${tableName}`;
            const response = await this.httpClient.delete(endpoint, {
                params: { workspaceId: this.options.workspaceId }
            });
            return response;
        }
        catch (error) {
            const ebaaError = error instanceof EBaaSError_1.EBaaSError ? error : new EBaaSError_1.EBaaSError(error instanceof Error ? error.message : 'Unknown error', 0, error);
            return {
                data: null,
                error: ebaaError,
                status: ebaaError.status,
                statusText: ebaaError.message
            };
        }
    }
}
exports.DatabaseClient = DatabaseClient;
//# sourceMappingURL=DatabaseClient.js.map