export interface QueryResponse<T = any> {
    data: T[] | null;
    error: Error | null;
    count?: number;
    status: number;
    statusText: string;
}
export interface InsertResponse<T = any> {
    data: T[] | null;
    error: Error | null;
    status: number;
    statusText: string;
}
export interface UpdateResponse<T = any> {
    data: T[] | null;
    error: Error | null;
    status: number;
    statusText: string;
}
export interface DeleteResponse {
    data: null;
    error: Error | null;
    status: number;
    statusText: string;
}
export interface RpcResponse<T = any> {
    data: T | null;
    error: Error | null;
    status: number;
    statusText: string;
}
export type DatabaseFilter = 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'ilike' | 'is' | 'in' | 'contains' | 'containedBy' | 'rangeGt' | 'rangeGte' | 'rangeLt' | 'rangeLte' | 'rangeAdjacent' | 'overlaps' | 'sl' | 'sr' | 'nxl' | 'nxr' | 'adj' | 'cd' | 'cs' | 'ov';
export type DatabaseOrder = 'asc' | 'desc';
export interface DatabaseOptions {
    schema: string;
    workspaceId: string;
}
export interface QueryOptions {
    head?: boolean;
    count?: 'exact' | 'planned' | 'estimated';
}
//# sourceMappingURL=types.d.ts.map