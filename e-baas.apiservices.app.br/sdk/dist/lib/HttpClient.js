"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpClient = void 0;
const axios_1 = __importDefault(require("axios"));
const EBaaSError_1 = require("./EBaaSError");
class HttpClient {
    constructor(options) {
        this.client = axios_1.default.create({
            baseURL: options.baseURL,
            timeout: options.timeout || 30000,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
        // Request interceptor
        this.client.interceptors.request.use((config) => {
            // Add any global request modifications here
            return config;
        }, (error) => {
            return Promise.reject(error);
        });
        // Response interceptor
        this.client.interceptors.response.use((response) => {
            return response;
        }, (error) => {
            if (error.response) {
                // Server responded with error status
                const ebaaError = new EBaaSError_1.EBaaSError(error.response.data?.error || error.response.data?.message || error.message, error.response.status, error.response.data);
                return Promise.reject(ebaaError);
            }
            else if (error.request) {
                // Request was made but no response received
                const ebaaError = new EBaaSError_1.EBaaSError('Network error - no response received', 0, { originalError: error });
                return Promise.reject(ebaaError);
            }
            else {
                // Something else happened
                const ebaaError = new EBaaSError_1.EBaaSError(error.message || 'Unknown error occurred', 0, { originalError: error });
                return Promise.reject(ebaaError);
            }
        });
    }
    setAuthToken(token) {
        this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }
    removeAuthToken() {
        delete this.client.defaults.headers.common['Authorization'];
    }
    async get(url, config) {
        try {
            const response = await this.client.get(url, config);
            return {
                data: response.data,
                status: response.status,
                statusText: response.statusText,
                error: null
            };
        }
        catch (error) {
            if (error instanceof EBaaSError_1.EBaaSError) {
                return {
                    data: null,
                    status: error.status,
                    statusText: error.message,
                    error
                };
            }
            throw error;
        }
    }
    async post(url, data, config) {
        try {
            const response = await this.client.post(url, data, config);
            return {
                data: response.data,
                status: response.status,
                statusText: response.statusText,
                error: null
            };
        }
        catch (error) {
            if (error instanceof EBaaSError_1.EBaaSError) {
                return {
                    data: null,
                    status: error.status,
                    statusText: error.message,
                    error
                };
            }
            throw error;
        }
    }
    async put(url, data, config) {
        try {
            const response = await this.client.put(url, data, config);
            return {
                data: response.data,
                status: response.status,
                statusText: response.statusText,
                error: null
            };
        }
        catch (error) {
            if (error instanceof EBaaSError_1.EBaaSError) {
                return {
                    data: null,
                    status: error.status,
                    statusText: error.message,
                    error
                };
            }
            throw error;
        }
    }
    async patch(url, data, config) {
        try {
            const response = await this.client.patch(url, data, config);
            return {
                data: response.data,
                status: response.status,
                statusText: response.statusText,
                error: null
            };
        }
        catch (error) {
            if (error instanceof EBaaSError_1.EBaaSError) {
                return {
                    data: null,
                    status: error.status,
                    statusText: error.message,
                    error
                };
            }
            throw error;
        }
    }
    async delete(url, config) {
        try {
            const response = await this.client.delete(url, config);
            return {
                data: response.data,
                status: response.status,
                statusText: response.statusText,
                error: null
            };
        }
        catch (error) {
            if (error instanceof EBaaSError_1.EBaaSError) {
                return {
                    data: null,
                    status: error.status,
                    statusText: error.message,
                    error
                };
            }
            throw error;
        }
    }
    // Raw axios instance for advanced usage
    get axios() {
        return this.client;
    }
}
exports.HttpClient = HttpClient;
//# sourceMappingURL=HttpClient.js.map