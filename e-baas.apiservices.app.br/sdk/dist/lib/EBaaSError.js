"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EBaaSError = void 0;
class EBaaSError extends Error {
    constructor(message, status = 0, details) {
        super(message);
        this.name = 'EBaaSError';
        this.status = status;
        this.details = details;
        // Maintain proper stack trace for where error was thrown (V8 only)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, EBaaSError);
        }
    }
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            status: this.status,
            details: this.details,
            stack: this.stack
        };
    }
    toString() {
        return `${this.name}: ${this.message} (Status: ${this.status})`;
    }
}
exports.EBaaSError = EBaaSError;
//# sourceMappingURL=EBaaSError.js.map