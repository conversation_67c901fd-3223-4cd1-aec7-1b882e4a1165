import { AxiosInstance, AxiosRequestConfig } from 'axios';
import { EBaaSResponse } from './types';
export interface HttpClientOptions {
    baseURL: string;
    headers?: Record<string, string>;
    timeout?: number;
}
export declare class HttpClient {
    private client;
    constructor(options: HttpClientOptions);
    setAuthToken(token: string): void;
    removeAuthToken(): void;
    get<T = any>(url: string, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>>;
    post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>>;
    put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>>;
    patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>>;
    delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<EBaaSResponse<T>>;
    get axios(): AxiosInstance;
}
//# sourceMappingURL=HttpClient.d.ts.map