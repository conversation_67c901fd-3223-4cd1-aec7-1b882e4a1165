# E-BaaS SDK

TypeScript client library for E-BaaS (Enterprise Backend as a Service) - A powerful Supabase alternative.

## Installation

```bash
npm install @e-baas/sdk
```

## Quick Start

```typescript
import { createClient } from '@e-baas/sdk'

// Initialize the client
const ebaas = createClient(
  'https://your-ebaas-instance.com',
  'your-api-key',
  {
    workspaceId: 'your-workspace-id'
  }
)

// Authentication
const { data, error } = await ebaas.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})

// Database queries
const { data: users } = await ebaas
  .from('users')
  .select('*')
  .eq('active', true)

// Storage operations
const { data: uploadResult } = await ebaas.storage
  .from('avatars')
  .upload('user1.jpg', file)

// Realtime subscriptions
ebaas.channel('public:users')
  .on('postgres_changes', { 
    event: 'INSERT', 
    schema: 'public', 
    table: 'users' 
  }, (payload) => {
    console.log('New user:', payload)
  })
  .subscribe()

// Edge functions
const { data: result } = await ebaas.functions.invoke('my-function', {
  body: { name: 'World' }
})
```

## API Reference

### Authentication

```typescript
// Sign up
const { data, error } = await ebaas.auth.signUp({
  email: '<EMAIL>',
  password: 'password',
  name: 'John Doe'
})

// Sign in with password
const { data, error } = await ebaas.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})

// Sign in with OAuth
const { data, error } = await ebaas.auth.signInWithOAuth({
  provider: 'google',
  redirectTo: 'https://your-app.com/callback'
})

// Get current user
const user = ebaas.auth.user()

// Sign out
await ebaas.auth.signOut()

// Listen to auth changes
ebaas.auth.onAuthStateChange((event, session) => {
  if (event === 'SIGNED_IN') {
    console.log('User signed in:', session.user)
  }
})
```

### Database

```typescript
// Select data
const { data, error } = await ebaas
  .from('posts')
  .select('title, content, author(name)')
  .eq('published', true)
  .order('created_at', { ascending: false })
  .limit(10)

// Insert data
const { data, error } = await ebaas
  .from('posts')
  .insert([
    { title: 'Hello World', content: 'First post' },
    { title: 'Second Post', content: 'Another post' }
  ])

// Update data
const { data, error } = await ebaas
  .from('posts')
  .update({ published: true })
  .eq('id', 1)

// Delete data
const { data, error } = await ebaas
  .from('posts')
  .delete()
  .eq('id', 1)

// Raw SQL
const { data, error } = await ebaas.database.query(
  'SELECT * FROM users WHERE created_at > $1',
  ['2023-01-01']
)

// RPC (Remote Procedure Call)
const { data, error } = await ebaas.rpc('get_user_stats', {
  user_id: 123
})
```

### Storage

```typescript
// Create bucket
const { data, error } = await ebaas.storage.createBucket('avatars', {
  public: true,
  fileSizeLimit: 1024 * 1024 * 5 // 5MB
})

// Upload file
const { data, error } = await ebaas.storage
  .from('avatars')
  .upload('user1.jpg', file, {
    contentType: 'image/jpeg',
    metadata: { user_id: '123' }
  })

// Download file
const { data, error } = await ebaas.storage
  .from('avatars')
  .download('user1.jpg')

// List files
const { data, error } = await ebaas.storage
  .from('avatars')
  .list('', {
    limit: 100,
    search: 'user'
  })

// Create signed URL
const { data, error } = await ebaas.storage
  .from('avatars')
  .createSignedUrl('user1.jpg', 3600) // 1 hour

// Get public URL
const { data } = ebaas.storage
  .from('avatars')
  .getPublicUrl('user1.jpg')

// Delete files
const { data, error } = await ebaas.storage
  .from('avatars')
  .remove(['user1.jpg', 'user2.jpg'])
```

### Realtime

```typescript
// Subscribe to table changes
const channel = ebaas.channel('public:posts')

channel
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'posts'
  }, (payload) => {
    console.log('Change received!', payload)
  })
  .subscribe()

// Broadcast messages
channel.send({
  type: 'broadcast',
  event: 'test',
  message: 'Hello everyone!'
})

// Presence tracking
channel.track({
  user_id: '123',
  online_at: new Date().toISOString()
})

// Unsubscribe
channel.unsubscribe()
```

### Edge Functions

```typescript
// Invoke function
const { data, error } = await ebaas.functions.invoke('hello-world', {
  body: { name: 'John' },
  headers: { 'Custom-Header': 'value' }
})

// List functions
const { data, error } = await ebaas.functions.list()

// Get function metadata
const { data, error } = await ebaas.functions.get('hello-world')
```

## Error Handling

```typescript
const { data, error } = await ebaas
  .from('posts')
  .select('*')

if (error) {
  console.error('Error:', error.message)
  console.error('Status:', error.status)
  console.error('Details:', error.details)
} else {
  console.log('Data:', data)
}
```

## TypeScript Support

The SDK is written in TypeScript and provides full type safety:

```typescript
interface User {
  id: string
  email: string
  name: string
  created_at: string
}

const { data, error } = await ebaas
  .from<User>('users')
  .select('*')
  
// data is typed as User[] | null
```

## Advanced Usage

### Custom Headers

```typescript
const ebaas = createClient(url, key, {
  workspaceId: 'your-workspace',
  headers: {
    'Custom-Header': 'value'
  }
})
```

### Auto Refresh Token

```typescript
const ebaas = createClient(url, key, {
  workspaceId: 'your-workspace',
  autoRefreshToken: true,
  persistSession: true
})
```

### Realtime Configuration

```typescript
const ebaas = createClient(url, key, {
  workspaceId: 'your-workspace',
  realtime: {
    heartbeatIntervalMs: 30000,
    reconnectDelayMs: 1000
  }
})
```

## Browser vs Node.js

The SDK works in both browser and Node.js environments. Some features like file uploads work differently:

### Browser
```typescript
// File input
const fileInput = document.querySelector('input[type="file"]')
const file = fileInput.files[0]

await ebaas.storage
  .from('uploads')
  .upload('file.pdf', file)
```

### Node.js
```typescript
import fs from 'fs'

// Read file
const fileBuffer = fs.readFileSync('file.pdf')

await ebaas.storage
  .from('uploads')
  .upload('file.pdf', fileBuffer)
```

## License

MIT

## Support

- Documentation: https://docs.e-baas.com
- GitHub: https://github.com/e-baas/sdk
- Discord: https://discord.gg/e-baas