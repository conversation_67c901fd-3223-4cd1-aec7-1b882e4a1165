import dotenv from "dotenv";

// Carrega as variáveis de ambiente
dotenv.config();

export default {
  app: {
    port: process.env.PORT || 3000,
    nodeEnv: process.env.NODE_ENV || "development",
    baseUrl: process.env.BASE_URL || "http://localhost:3000",
  },
  database: {
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT || "5432"),
    username: process.env.DB_USERNAME || "postgres",
    password: process.env.DB_PASSWORD || "postgres",
    database: process.env.DB_DATABASE || "ebaas_main",
    type: process.env.NODE_ENV === "test" ? "sqlite" : "postgres",
    databaseFile: "src/infra/database/data.sqlite",
  },
  jwt: {
    secret: process.env.JWT_SECRET || "your_jwt_secret_key",
    expiresIn: process.env.JWT_EXPIRES_IN || "15m",
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || "7d",
  },
  security: {
    bcryptSaltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS || "10"),
  },
  api: {
    keyPrefix: process.env.API_KEY_PREFIX || "ebaas_",
  },
  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      redirectUri: process.env.GOOGLE_REDIRECT_URI,
    },
    github: {
      clientId: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
      redirectUri: process.env.GITHUB_REDIRECT_URI,
    },
    facebook: {
      clientId: process.env.FACEBOOK_CLIENT_ID,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
      redirectUri: process.env.FACEBOOK_REDIRECT_URI,
    },
  },
  cdn: {
    enabled: process.env.CDN_ENABLED === "true",
    provider: process.env.CDN_PROVIDER || "cloudflare", // cloudflare, aws, custom
    baseUrl: process.env.CDN_BASE_URL,
    cacheEnabled: process.env.CDN_CACHE_ENABLED !== "false",
    cacheMaxAge: parseInt(process.env.CDN_CACHE_MAX_AGE || "3600"), // 1 hour default
    purgeOnUpdate: process.env.CDN_PURGE_ON_UPDATE !== "false",
    // Cloudflare specific
    cloudflare: {
      apiToken: process.env.CLOUDFLARE_API_TOKEN,
      zoneId: process.env.CLOUDFLARE_ZONE_ID,
      accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
    },
    // AWS CloudFront specific
    aws: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || "us-east-1",
      distributionId: process.env.AWS_CLOUDFRONT_DISTRIBUTION_ID,
    },
    // Custom CDN settings
    custom: {
      apiEndpoint: process.env.CUSTOM_CDN_API_ENDPOINT,
      apiKey: process.env.CUSTOM_CDN_API_KEY,
      purgeEndpoint: process.env.CUSTOM_CDN_PURGE_ENDPOINT,
    },
  },
  supportedDatabases: ["postgres", "mysql", "mongodb"],
};
