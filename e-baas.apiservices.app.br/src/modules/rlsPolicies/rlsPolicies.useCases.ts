import { RlsPolicies } from "./entity/rls-policies.entity";
import { rls-policiesRepository } from "infra/repository";
import IContractUseCases from "infra/contracts";
import { ErrorHandler } from "infra/errorHandlers";

export default class RlsPoliciesUseCases implements IContractUseCases<RlsPolicies> {
    constructor() {}

    async getAll(): Promise<RlsPolicies[]> {
        try {
            return await rls-policiesRepository.find();
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async getOne(id: string): Promise<RlsPolicies> {
        try {
            const rls-policies = await rls-policiesRepository.findOneBy({ id });
            if (!rls-policies) {
                throw ErrorHandler.NotFound("RlsPolicies not found");
            }
            return rls-policies;
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "RlsPolicies not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async create(data: Partial<RlsPolicies>): Promise<RlsPolicies> {
        try {
            return await rls-policiesRepository.save(data);
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async update(id: string, data: Partial<RlsPolicies>): Promise<RlsPolicies> {
        try {
            const rls-policies = await this.getOne(id);
            await rls-policiesRepository.update(id, data);
            return { ...rls-policies, ...data };
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "RlsPolicies not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await this.getOne(id);
            await rls-policiesRepository.delete(id);
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "RlsPolicies not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }
}