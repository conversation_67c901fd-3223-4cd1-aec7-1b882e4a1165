import { Router } from "express";
import { validator } from "infra";

import RlsPoliciesService from "modules/rls-policies/rls-policies.service";
import RlsPoliciesUseCases from "modules/rls-policies/rls-policies.useCases"; 
import { RlsPoliciesDto } from "modules/rls-policies/dto/rls-policies.dto";

const controller = Router();
const rls-policiesUseCases = new RlsPoliciesUseCases(); 
const rls-policiesService = new RlsPoliciesService(rls-policiesUseCases);

controller.get("/", (req, res) => rls-policiesService.getAll(req, res));
controller.get("/:id", (req, res) => rls-policiesService.getOne(req, res));
controller.post("/", validator(RlsPoliciesDto), (req, res) => rls-policiesService.create(req, res));
controller.put("/:id", validator(RlsPoliciesDto), (req, res) => rls-policiesService.update(req, res));
controller.delete("/:id", (req, res) => rls-policiesService.delete(req, res));

export default controller;