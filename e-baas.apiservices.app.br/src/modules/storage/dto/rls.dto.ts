import { <PERSON>String, <PERSON><PERSON><PERSON>, IsArray, IsOptional, IsBoolean, IsNumber, IsObject, ValidateNested } from "class-validator";
import { Type } from "class-transformer";
import { StorageFile } from "../entity/StorageFile.entity";
import { Bucket } from "../entity/Bucket.entity";
import { User } from "../../auth/entity/user.entity";
import { StoragePolicy } from "../entity/StoragePolicy.entity";

// Enums
export enum PolicyEffect {
  ALLOW = "allow",
  DENY = "deny"
}

export enum PolicyAction {
  ALL = "*",
  CREATE = "create",
  READ = "read",
  UPDATE = "update",
  DELETE = "delete",
  LIST = "list",
  UPLOAD = "upload",
  DOWNLOAD = "download",
  COPY = "copy",
  MOVE = "move"
}

export enum PolicyOperator {
  EQ = "eq",
  NE = "ne",
  IN = "in",
  NOT_IN = "not_in",
  STARTS_WITH = "starts_with",
  ENDS_WITH = "ends_with",
  CONTAINS = "contains",
  GT = "gt",
  GTE = "gte",
  LT = "lt",
  LTE = "lte",
  REGEX = "regex",
  EXISTS = "exists",
  TIME_BETWEEN = "time_between",
  IP_IN_RANGE = "ip_in_range"
}

// Interfaces
export interface PolicyCondition {
  field: string;
  operator: keyof typeof PolicyOperator | string;
  value: any;
}

export interface StorageAccessContext {
  user: User;
  file?: StorageFile;
  bucket: Bucket;
  action: PolicyAction;
  workspaceId: string;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
  [key: string]: any;
}

export interface PolicyEvaluationResult {
  allowed: boolean;
  reason: string;
  matchedPolicies: StoragePolicy[];
}

// DTOs
export class CreatePolicyDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(PolicyEffect)
  effect: PolicyEffect;

  @IsArray()
  @IsEnum(PolicyAction, { each: true })
  actions: PolicyAction[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PolicyConditionDto)
  conditions: PolicyConditionDto[];

  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsString()
  bucketName?: string;

  @IsOptional()
  @IsNumber()
  priority?: number;
}

export class PolicyConditionDto implements PolicyCondition {
  @IsString()
  field: string;

  @IsString()
  operator: string;

  value: any;
}

export class UpdatePolicyDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(PolicyEffect)
  effect?: PolicyEffect;

  @IsOptional()
  @IsArray()
  @IsEnum(PolicyAction, { each: true })
  actions?: PolicyAction[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PolicyConditionDto)
  conditions?: PolicyConditionDto[];

  @IsOptional()
  @IsNumber()
  priority?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class CheckAccessDto {
  @IsString()
  fileId?: string;

  @IsString()
  bucketName?: string;

  @IsString()
  workspaceId: string;

  @IsEnum(PolicyAction)
  action: PolicyAction;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsObject()
  additionalContext?: Record<string, any>;
}

export class TestPolicyDto {
  @IsString()
  policyId: string;

  @IsOptional()
  @IsObject()
  testContext?: {
    userId?: string;
    bucketName?: string;
    fileName?: string;
    action?: PolicyAction;
    workspaceId?: string;
    userAgent?: string;
    ipAddress?: string;
    timestamp?: string;
    [key: string]: any;
  };
}

export class BulkPolicyOperationDto {
  @IsArray()
  @IsString({ each: true })
  policyIds: string[];

  @IsEnum(['activate', 'deactivate', 'delete'])
  operation: 'activate' | 'deactivate' | 'delete';
}

export class PolicyTemplateDto {
  @IsEnum(['owner_only', 'public_read', 'authenticated_only', 'workspace_team', 'time_based', 'ip_restricted'])
  template: 'owner_only' | 'public_read' | 'authenticated_only' | 'workspace_team' | 'time_based' | 'ip_restricted';

  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsString()
  bucketName?: string;

  @IsOptional()
  @IsObject()
  parameters?: {
    startTime?: string;
    endTime?: string;
    allowedCIDRs?: string[];
    [key: string]: any;
  };
}

export class PolicyAuditDto {
  @IsOptional()
  @IsString()
  workspaceId?: string;

  @IsOptional()
  @IsString()
  bucketName?: string;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsEnum(PolicyAction)
  action?: PolicyAction;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsNumber()
  limit?: number;

  @IsOptional()
  @IsNumber()
  offset?: number;
}

// Response DTOs
export interface PolicyListResponse {
  policies: StoragePolicy[];
  total: number;
  page: number;
  limit: number;
}

export interface AccessCheckResponse {
  allowed: boolean;
  reason: string;
  matchedPolicies: {
    id: string;
    name: string;
    effect: PolicyEffect;
    priority: number;
  }[];
  evaluationTime: number;
}

export interface PolicyTestResponse {
  passed: boolean;
  reason: string;
  conditionsEvaluated: {
    field: string;
    operator: string;
    expectedValue: any;
    actualValue: any;
    result: boolean;
  }[];
}

export interface PolicyAuditEntry {
  id: string;
  timestamp: Date;
  userId: string;
  workspaceId: string;
  bucketName?: string;
  fileName?: string;
  action: PolicyAction;
  result: 'allowed' | 'denied';
  reason: string;
  matchedPolicies: string[];
  userAgent?: string;
  ipAddress?: string;
}

export interface PolicyStatsResponse {
  totalPolicies: number;
  activePolicies: number;
  inactivePolicies: number;
  policiesByEffect: {
    allow: number;
    deny: number;
  };
  policiesByScope: {
    global: number;
    bucket: number;
  };
  recentEvaluations: {
    total: number;
    allowed: number;
    denied: number;
  };
}

// Predefined condition templates
export const CommonConditions = {
  // User-based conditions
  OWNER_ONLY: (userId: string): PolicyCondition => ({
    field: 'file.ownerId',
    operator: 'eq',
    value: userId
  }),

  AUTHENTICATED_USER: (): PolicyCondition => ({
    field: 'user.id',
    operator: 'exists',
    value: true
  }),

  WORKSPACE_MEMBER: (workspaceId: string): PolicyCondition => ({
    field: 'user.workspaceId',
    operator: 'eq',
    value: workspaceId
  }),

  // Time-based conditions
  BUSINESS_HOURS: (): PolicyCondition => ({
    field: 'timestamp',
    operator: 'time_between',
    value: ['09:00', '17:00']
  }),

  WEEKDAYS_ONLY: (): PolicyCondition => ({
    field: 'timestamp.getDay()',
    operator: 'in',
    value: [1, 2, 3, 4, 5]
  }),

  // File-based conditions
  FILE_SIZE_LIMIT: (maxSize: number): PolicyCondition => ({
    field: 'file.size',
    operator: 'lte',
    value: maxSize
  }),

  FILE_TYPE_WHITELIST: (allowedTypes: string[]): PolicyCondition => ({
    field: 'file.mimeType',
    operator: 'in',
    value: allowedTypes
  }),

  FILE_PATH_PREFIX: (prefix: string): PolicyCondition => ({
    field: 'file.path',
    operator: 'starts_with',
    value: prefix
  }),

  // Network-based conditions
  IP_WHITELIST: (allowedIPs: string[]): PolicyCondition => ({
    field: 'ipAddress',
    operator: 'in',
    value: allowedIPs
  }),

  INTERNAL_NETWORK: (): PolicyCondition => ({
    field: 'ipAddress',
    operator: 'ip_in_range',
    value: '10.0.0.0/8'
  })
};