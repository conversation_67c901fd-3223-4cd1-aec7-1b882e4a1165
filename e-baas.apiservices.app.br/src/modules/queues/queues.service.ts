import { QueueManager } from '@planify/queues';
import { PublishOptions, QueueStatus, QueueMessage } from './dto/queues.dto';
import config from '@/infra/config';

export class QueuesService {
  private queueManager: QueueManager;
  private consumers: Map<string, boolean> = new Map();

  constructor() {
    // Configurar conexão Redis a partir do config
    const redisConfig = {
      host: config.redis?.host || process.env.REDIS_HOST || 'localhost',
      port: config.redis?.port || parseInt(process.env.REDIS_PORT || '6379'),
      username: config.redis?.username || process.env.REDIS_USERNAME || 'publisher',
      password: config.redis?.password || process.env.REDIS_PASSWORD || 'redis123',
    };

    this.queueManager = new QueueManager(redisConfig);
    console.log('✅ Queue Manager initialized with Redis config:', { 
      host: redisConfig.host, 
      port: redisConfig.port 
    });
  }

  async publish(eventType: string, data: any, options?: PublishOptions): Promise<string> {
    try {
      const result = await this.queueManager.publish(eventType, data, options);
      console.log(`📤 Message published to queue '${eventType}':`, { 
        messageId: result, 
        options 
      });
      return result;
    } catch (error) {
      console.error(`❌ Failed to publish message to queue '${eventType}':`, error);
      throw error;
    }
  }

  async getQueueStatus(queueName: string): Promise<QueueStatus> {
    try {
      const status = await this.queueManager.getQueueStatus();
      return status[queueName] || {
        waiting: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        total: 0
      };
    } catch (error) {
      console.error(`❌ Failed to get status for queue '${queueName}':`, error);
      throw error;
    }
  }

  async getAllQueuesStatus(): Promise<Record<string, QueueStatus>> {
    try {
      return await this.queueManager.getQueueStatus();
    } catch (error) {
      console.error('❌ Failed to get status for all queues:', error);
      throw error;
    }
  }

  async getQueueMessages(queueName: string, status: string): Promise<QueueMessage[]> {
    try {
      const messages = await this.queueManager.getQueueMessages(queueName, status);
      return messages.map(msg => ({
        id: msg.id,
        data: msg.data,
        status: msg.status,
        createdAt: msg.createdAt,
        updatedAt: msg.updatedAt,
        attempts: msg.attempts,
        maxAttempts: msg.maxAttempts,
        priority: msg.priority
      }));
    } catch (error) {
      console.error(`❌ Failed to get messages for queue '${queueName}':`, error);
      throw error;
    }
  }

  async getPendingMessages(queueName: string): Promise<QueueMessage[]> {
    try {
      const messages = await this.queueManager.getPendingMessages(queueName);
      return messages.map(msg => ({
        id: msg.id,
        data: msg.data,
        status: msg.status,
        createdAt: msg.createdAt,
        updatedAt: msg.updatedAt,
        attempts: msg.attempts,
        maxAttempts: msg.maxAttempts,
        priority: msg.priority
      }));
    } catch (error) {
      console.error(`❌ Failed to get pending messages for queue '${queueName}':`, error);
      throw error;
    }
  }

  async getCompletedMessages(queueName: string): Promise<QueueMessage[]> {
    try {
      const messages = await this.queueManager.getCompletedMessages(queueName);
      return messages.map(msg => ({
        id: msg.id,
        data: msg.data,
        status: msg.status,
        createdAt: msg.createdAt,
        updatedAt: msg.updatedAt,
        attempts: msg.attempts,
        maxAttempts: msg.maxAttempts,
        priority: msg.priority
      }));
    } catch (error) {
      console.error(`❌ Failed to get completed messages for queue '${queueName}':`, error);
      throw error;
    }
  }

  async startConsumer(queueName: string, handler: (message: any) => Promise<void>): Promise<void> {
    try {
      if (this.consumers.get(queueName)) {
        console.log(`⚠️ Consumer for queue '${queueName}' is already running`);
        return;
      }

      await this.queueManager.consumer(queueName, handler);
      this.consumers.set(queueName, true);
      console.log(`✅ Consumer started for queue '${queueName}'`);
    } catch (error) {
      console.error(`❌ Failed to start consumer for queue '${queueName}':`, error);
      throw error;
    }
  }

  async stopConsumer(queueName: string): Promise<void> {
    try {
      // O SDK não expõe um método para parar consumidores específicos
      // Por enquanto, apenas marcar como parado
      this.consumers.delete(queueName);
      console.log(`🛑 Consumer stopped for queue '${queueName}'`);
    } catch (error) {
      console.error(`❌ Failed to stop consumer for queue '${queueName}':`, error);
      throw error;
    }
  }

  isConsumerRunning(queueName: string): boolean {
    return this.consumers.get(queueName) || false;
  }

  getActiveConsumers(): string[] {
    return Array.from(this.consumers.keys());
  }

  async healthCheck(): Promise<{ status: string; redis: boolean; consumers: string[] }> {
    try {
      // Verificar se o Redis está acessível tentando obter status das filas
      await this.getAllQueuesStatus();
      
      return {
        status: 'healthy',
        redis: true,
        consumers: this.getActiveConsumers()
      };
    } catch (error) {
      console.error('❌ Queue service health check failed:', error);
      return {
        status: 'unhealthy',
        redis: false,
        consumers: this.getActiveConsumers()
      };
    }
  }
}