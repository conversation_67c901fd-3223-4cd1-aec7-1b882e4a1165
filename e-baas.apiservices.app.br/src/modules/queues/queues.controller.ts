import { Request, Response } from "express";
import { QueueManager } from "sdk-queues";
import { QueuesUseCases } from "./queues.useCases";

export class QueuesController {
  private queuesUseCases: QueuesUseCases;

  constructor() {
    this.queuesUseCases = new QueuesUseCases();
  }

  async publishMessage(req: Request, res: Response): Promise<void> {
    try {
      const { eventType, data, options } = req.body;

      if (!eventType || !data) {
        res.status(400).json({
          error: "eventType and data are required",
        });
        return;
      }

      const result = await this.queuesUseCases.publishMessage(
        eventType,
        data,
        options
      );

      res.status(201).json({
        success: true,
        messageId: result,
      });
    } catch (error) {
      res.status(500).json({
        error: "Failed to publish message",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  async getQueueStatus(req: Request, res: Response): Promise<void> {
    try {
      const { queueName } = req.params;

      const status = await this.queuesUseCases.getQueueStatus(queueName);

      res.json({
        success: true,
        data: status,
      });
    } catch (error) {
      res.status(500).json({
        error: "Failed to get queue status",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  async getQueueMessages(req: Request, res: Response): Promise<void> {
    try {
      const { queueName } = req.params;
      const { status = "all" } = req.query;

      const messages = await this.queuesUseCases.getQueueMessages(
        queueName,
        status as string
      );

      res.json({
        success: true,
        data: messages,
      });
    } catch (error) {
      res.status(500).json({
        error: "Failed to get queue messages",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  async getPendingMessages(req: Request, res: Response): Promise<void> {
    try {
      const { queueName } = req.params;

      const messages = await this.queuesUseCases.getPendingMessages(queueName);

      res.json({
        success: true,
        data: messages,
      });
    } catch (error) {
      res.status(500).json({
        error: "Failed to get pending messages",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  async getCompletedMessages(req: Request, res: Response): Promise<void> {
    try {
      const { queueName } = req.params;

      const messages = await this.queuesUseCases.getCompletedMessages(
        queueName
      );

      res.json({
        success: true,
        data: messages,
      });
    } catch (error) {
      res.status(500).json({
        error: "Failed to get completed messages",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  async monitorQueue(req: Request, res: Response): Promise<void> {
    try {
      const { queueName } = req.params;

      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");
      res.setHeader("Access-Control-Allow-Origin", "*");
      res.setHeader("Access-Control-Allow-Headers", "Cache-Control");

      const sendUpdate = async () => {
        try {
          const status = await this.queuesUseCases.getQueueStatus(queueName);
          res.write(`data: ${JSON.stringify(status)}\n\n`);
        } catch (error) {
          console.error("Error getting queue status for SSE:", error);
        }
      };

      // Enviar status inicial
      await sendUpdate();

      // Atualizar a cada 5 segundos
      const interval = setInterval(sendUpdate, 5000);

      // Limpar quando a conexão fechar
      req.on("close", () => {
        clearInterval(interval);
      });
    } catch (error) {
      res.status(500).json({
        error: "Failed to start queue monitoring",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
}
