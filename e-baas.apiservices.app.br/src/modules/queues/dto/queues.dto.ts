import { IsString, IsOptional, IsN<PERSON>ber, IsBoolean, IsObject, Min, Max } from 'class-validator';

export class PublishMessageDto {
  @IsString()
  eventType!: string;

  @IsObject()
  data!: any;

  @IsOptional()
  options?: PublishOptions;
}

export class GetQueueMessagesDto {
  @IsString()
  queueName!: string;

  @IsOptional()
  @IsString()
  status?: 'all' | 'pending' | 'processing' | 'completed' | 'failed';
}

export interface PublishOptions {
  persistent?: boolean;
  priority?: number;
  attempts?: number;
}

export interface QueueStatus {
  waiting: number;
  processing: number;
  completed: number;
  failed: number;
  total: number;
}

export interface QueueMessage {
  id: string;
  data: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: Date | string;
  updatedAt: Date | string;
  attempts: number;
  maxAttempts: number;
  priority: number;
}

export class CreateQueueEventDto {
  @IsString()
  eventType!: string;

  @IsObject()
  data!: {
    payload: any;
  };

  @IsOptional()
  options?: {
    @IsOptional()
    @IsBoolean()
    persistent?: boolean;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(10)
    priority?: number;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(10)
    attempts?: number;
  };
}

export interface QueueHealthCheck {
  status: 'healthy' | 'unhealthy';
  redis: boolean;
  consumers: string[];
  timestamp: Date;
}

export interface ConsumerInfo {
  queueName: string;
  isRunning: boolean;
  startedAt?: Date;
}

export interface QueueMetrics {
  totalQueues: number;
  totalMessages: number;
  totalConsumers: number;
  activeConsumers: string[];
  queueStats: Record<string, QueueStatus>;
}

export class QueueMonitoringDto {
  @IsString()
  queueName!: string;

  @IsOptional()
  @IsNumber()
  intervalMs?: number;
}

export interface EventTypeConfig {
  name: string;
  description: string;
  defaultPriority: number;
  maxAttempts: number;
  handler?: string;
}

export const DEFAULT_EVENT_TYPES: EventTypeConfig[] = [
  {
    name: 'email',
    description: 'Envio de emails',
    defaultPriority: 2,
    maxAttempts: 3
  },
  {
    name: 'notification',
    description: 'Notificações do sistema',
    defaultPriority: 1,
    maxAttempts: 3
  },
  {
    name: 'payment',
    description: 'Processamento de pagamentos',
    defaultPriority: 3,
    maxAttempts: 5
  },
  {
    name: 'order',
    description: 'Processamento de pedidos',
    defaultPriority: 2,
    maxAttempts: 3
  },
  {
    name: 'analytics',
    description: 'Eventos de analytics',
    defaultPriority: 1,
    maxAttempts: 1
  },
  {
    name: 'webhook',
    description: 'Webhooks externos',
    defaultPriority: 2,
    maxAttempts: 3
  }
];