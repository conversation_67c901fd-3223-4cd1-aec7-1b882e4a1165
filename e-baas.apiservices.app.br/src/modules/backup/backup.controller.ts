import { Router, Request, Response } from "express";
import { BackupService, PlatformBackupOptions, WorkspaceBackupOptions } from "./backup.service";
import { 
  requireAdmin, 
  requirePlatformAdmin, 
  requireWorkspaceAdmin,
  scopeToWorkspace,
  AdminRequest 
} from "../../infra/middlewares/admin-auth.middleware";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";
import { CreateBackupDto, BackupFilterDto } from "./dto/backup.dto";
import * as path from 'path';

const backupRouter = Router();
const backupService = new BackupService();

// Helper function for validation
const validateDto = async (DtoClass: any, data: any) => {
  const dto = plainToClass(DtoClass, data);
  const errors = await validate(dto);
  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
  }
  return dto;
};

// ==== PLATFORM ADMIN BACKUP ROUTES ====

/**
 * @swagger
 * /admin/v1/platform/backups:
 *   post:
 *     summary: Create platform backup (Platform Admin only)
 *     tags: [Platform Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               adminOnly:
 *                 type: boolean
 *                 description: Backup only admin data, not client workspaces
 *               includeDatabase:
 *                 type: boolean
 *                 default: true
 *               includeStorage:
 *                 type: boolean
 *                 default: true
 *               includeUsers:
 *                 type: boolean
 *                 default: true
 *               includeWorkspaces:
 *                 type: boolean
 *                 default: true
 *               compressionLevel:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 9
 *                 default: 6
 */
backupRouter.post("/platform/backups", requirePlatformAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const createBackupDto = await validateDto(CreateBackupDto, req.body);
    
    const options: PlatformBackupOptions = {
      adminOnly: createBackupDto.adminOnly || false,
      includeDatabase: createBackupDto.includeDatabase,
      includeStorage: createBackupDto.includeStorage,
      includeUsers: createBackupDto.includeUsers,
      includeWorkspaces: createBackupDto.includeWorkspaces,
      compressionLevel: createBackupDto.compressionLevel
    };

    const backup = await backupService.createPlatformBackup(options);
    
    return res.status(201).json({
      message: 'Platform backup created successfully',
      backup: {
        id: backup.id,
        filename: backup.filename,
        size: backup.size,
        type: backup.type,
        createdAt: backup.createdAt,
        metadata: backup.metadata
      }
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /admin/v1/platform/backups:
 *   get:
 *     summary: List all platform backups (Platform Admin only)
 *     tags: [Platform Admin - Backups]
 *     security:
 *       - bearerAuth: []
 */
backupRouter.get("/platform/backups", requirePlatformAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const backups = await backupService.listBackups();
    
    return res.status(200).json({
      backups: backups.map(backup => ({
        id: backup.id,
        filename: backup.filename,
        size: backup.size,
        type: backup.type,
        workspaceId: backup.workspaceId,
        createdAt: backup.createdAt,
        metadata: backup.metadata
      }))
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /admin/v1/platform/backups/{backupId}/download:
 *   get:
 *     summary: Download platform backup (Platform Admin only)
 *     tags: [Platform Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: backupId
 *         required: true
 *         schema:
 *           type: string
 */
backupRouter.get("/platform/backups/:backupId/download", requirePlatformAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { backupId } = req.params;
    const backupPath = await backupService.downloadBackup(backupId);
    
    const filename = path.basename(backupPath);
    
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'application/zip');
    
    return res.download(backupPath, filename);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /admin/v1/platform/backups/{backupId}:
 *   delete:
 *     summary: Delete platform backup (Platform Admin only)
 *     tags: [Platform Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: backupId
 *         required: true
 *         schema:
 *           type: string
 */
backupRouter.delete("/platform/backups/:backupId", requirePlatformAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { backupId } = req.params;
    await backupService.deleteBackup(backupId);
    
    return res.status(200).json({
      message: 'Backup deleted successfully'
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// ==== WORKSPACE ADMIN BACKUP ROUTES ====

/**
 * @swagger
 * /admin/v1/workspace/backups:
 *   post:
 *     summary: Create workspace backup (Workspace Admin only)
 *     tags: [Workspace Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               includeDatabase:
 *                 type: boolean
 *                 default: true
 *               includeStorage:
 *                 type: boolean
 *                 default: true
 *               includeUsers:
 *                 type: boolean
 *                 default: true
 *               compressionLevel:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 9
 *                 default: 6
 */
backupRouter.post("/workspace/backups", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const createBackupDto = await validateDto(CreateBackupDto, req.body);
    const workspaceId = req.user?.workspaceId;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID required' });
    }

    const options: WorkspaceBackupOptions = {
      workspaceId,
      includeDatabase: createBackupDto.includeDatabase,
      includeStorage: createBackupDto.includeStorage,
      includeUsers: createBackupDto.includeUsers,
      includeWorkspaces: createBackupDto.includeWorkspaces,
      compressionLevel: createBackupDto.compressionLevel
    };

    const backup = await backupService.createWorkspaceBackup(options);
    
    return res.status(201).json({
      message: 'Workspace backup created successfully',
      backup: {
        id: backup.id,
        filename: backup.filename,
        size: backup.size,
        type: backup.type,
        workspaceId: backup.workspaceId,
        createdAt: backup.createdAt,
        metadata: backup.metadata
      }
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /admin/v1/workspace/backups:
 *   get:
 *     summary: List workspace backups (Workspace Admin only)
 *     tags: [Workspace Admin - Backups]
 *     security:
 *       - bearerAuth: []
 */
backupRouter.get("/workspace/backups", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const workspaceId = req.user?.workspaceId;
    const backups = await backupService.listBackups(workspaceId);
    
    return res.status(200).json({
      backups: backups.map(backup => ({
        id: backup.id,
        filename: backup.filename,
        size: backup.size,
        type: backup.type,
        createdAt: backup.createdAt,
        metadata: backup.metadata
      }))
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /admin/v1/workspace/backups/{backupId}/download:
 *   get:
 *     summary: Download workspace backup (Workspace Admin only)
 *     tags: [Workspace Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: backupId
 *         required: true
 *         schema:
 *           type: string
 */
backupRouter.get("/workspace/backups/:backupId/download", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const { backupId } = req.params;
    const workspaceId = req.user?.workspaceId;
    
    const backupPath = await backupService.downloadBackup(backupId, workspaceId);
    const filename = path.basename(backupPath);
    
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'application/zip');
    
    return res.download(backupPath, filename);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /admin/v1/workspace/backups/{backupId}:
 *   delete:
 *     summary: Delete workspace backup (Workspace Admin only)
 *     tags: [Workspace Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: backupId
 *         required: true
 *         schema:
 *           type: string
 */
backupRouter.delete("/workspace/backups/:backupId", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const { backupId } = req.params;
    const workspaceId = req.user?.workspaceId;
    
    await backupService.deleteBackup(backupId, workspaceId);
    
    return res.status(200).json({
      message: 'Backup deleted successfully'
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// ==== BACKUP STATUS & INFO ROUTES ====

/**
 * @swagger
 * /admin/v1/backups/status:
 *   get:
 *     summary: Get backup system status
 *     tags: [Admin - Backups]
 *     security:
 *       - bearerAuth: []
 */
backupRouter.get("/backups/status", requireAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const workspaceId = req.user?.isPlatformAdmin ? undefined : req.user?.workspaceId;
    const backups = await backupService.listBackups(workspaceId);
    
    const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
    const lastBackup = backups[0];
    
    return res.status(200).json({
      totalBackups: backups.length,
      totalSize,
      lastBackup: lastBackup ? {
        id: lastBackup.id,
        type: lastBackup.type,
        createdAt: lastBackup.createdAt,
        size: lastBackup.size
      } : null,
      systemStatus: 'operational',
      backupDirectory: process.env.BACKUP_DIRECTORY || './backups'
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

export default backupRouter;