import { Repository } from "typeorm";
import { AppDataSource } from "../../infra/database/data-source";
import { User } from "../users/entity/user.entity";
import { RefreshToken } from "./entity/RefreshToken.entity";
import {
  SignInDto,
  SignUpDto,
  RefreshTokenDto,
  ResetPasswordDto,
  UpdatePasswordDto,
  VerifyEmailDto,
  MagicLinkDto,
  OAuthDto,
  UpdateUserDto,
  CreateSessionDto,
  UserRole,
  AuthProvider
} from "./dto/auth.dto";
import { OAuthService, OAuthUserInfo } from "./oauth.service";
import { EmailService } from "./email.service";
import jwt from "jsonwebtoken";
import config from "config";
import { v4 as uuidv4 } from "uuid";
import crypto from "crypto";

interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  expiresAt: number;
  tokenType: string;
}

interface Session {
  id: string;
  userId: string;
  workspaceId?: string;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
  isActive: boolean;
  createdAt: Date;
  lastAccessedAt: Date;
}

export class AuthService {
  private userRepository: Repository<User>;
  private refreshTokenRepository: Repository<RefreshToken>;
  private sessions: Map<string, Session> = new Map();
  private oauthService: OAuthService;
  private emailService: EmailService;

  constructor() {
    this.userRepository = AppDataSource.getRepository(User);
    this.refreshTokenRepository = AppDataSource.getRepository(RefreshToken);
    this.oauthService = new OAuthService();
    this.emailService = new EmailService();
  }

  async signUp(signUpDto: SignUpDto): Promise<AuthResponse> {
    const { email, password, workspaceId = 'default', firstName, lastName, metadata, emailConfirm = false } = signUpDto;

    // Check if user already exists in workspace
    const existingUser = await this.userRepository.findOne({
      where: { email, workspaceId }
    });

    if (existingUser) {
      throw new Error('User already exists in this workspace');
    }

    // Create new user
    const user = this.userRepository.create({
      email,
      password,
      firstName,
      lastName,
      workspaceId,
      role: UserRole.AUTHENTICATED,
      authProvider: AuthProvider.EMAIL,
      emailVerified: emailConfirm,
      confirmedAt: emailConfirm ? new Date() : undefined,
      rawUserMetaData: metadata,
      signInCount: 0,
      isActive: true
    });

    // Generate email verification token if needed
    if (!emailConfirm) {
      user.emailVerifyToken = this.generateSecureToken();
    }

    await this.userRepository.save(user);

    // Generate tokens
    const { accessToken, refreshToken, expiresIn, expiresAt } = await this.generateTokens(user);

    return {
      user: user.toSafeObject() as User,
      accessToken,
      refreshToken,
      expiresIn,
      expiresAt,
      tokenType: 'Bearer'
    };
  }

  async signIn(signInDto: SignInDto): Promise<AuthResponse> {
    const { email, password, workspaceId = 'default' } = signInDto;

    // Find user with password
    const user = await this.userRepository.findOne({
      where: { email, workspaceId },
      select: ['id', 'email', 'password', 'firstName', 'lastName', 'workspaceId', 'role', 'emailVerified', 'isActive', 'isBanned']
    });

    if (!user) {
      throw new Error('Invalid credentials');
    }

    // Check if user is active and not banned
    if (!user.isActive) {
      throw new Error('Account is deactivated');
    }

    if (user.isBanned) {
      throw new Error('Account is temporarily banned');
    }

    // Verify password
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }

    // Update login stats
    user.lastSignIn = new Date();
    user.signInCount += 1;
    await this.userRepository.save(user);

    // Generate tokens
    const { accessToken, refreshToken, expiresIn, expiresAt } = await this.generateTokens(user);

    return {
      user: user.toSafeObject() as User,
      accessToken,
      refreshToken,
      expiresIn,
      expiresAt,
      tokenType: 'Bearer'
    };
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<Omit<AuthResponse, 'user'>> {
    const { refreshToken: token, workspaceId = 'default' } = refreshTokenDto;

    // Find refresh token
    const refreshTokenEntity = await this.refreshTokenRepository.findOne({
      where: { token, workspaceId },
      relations: ['user']
    });

    if (!refreshTokenEntity || !refreshTokenEntity.isValid()) {
      throw new Error('Invalid or expired refresh token');
    }

    // Update last used
    refreshTokenEntity.updateLastUsed();
    await this.refreshTokenRepository.save(refreshTokenEntity);

    // Check if user is still active
    if (!refreshTokenEntity.user.isActive) {
      throw new Error('User account is deactivated');
    }

    // Generate new tokens (refresh token rotation)
    const { accessToken, refreshToken: newRefreshToken, expiresIn, expiresAt } = 
      await this.generateTokens(refreshTokenEntity.user);

    // Revoke old refresh token
    refreshTokenEntity.revoke();
    await this.refreshTokenRepository.save(refreshTokenEntity);

    return {
      accessToken,
      refreshToken: newRefreshToken,
      expiresIn,
      expiresAt,
      tokenType: 'Bearer'
    };
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    const { email, workspaceId = 'default' } = resetPasswordDto;

    const user = await this.userRepository.findOne({
      where: { email, workspaceId }
    });

    if (!user) {
      // Don't reveal if user exists for security
      return { message: 'If the email exists, a password reset link has been sent' };
    }

    // Generate reset token
    const resetToken = this.generateSecureToken();
    const resetExpires = new Date(Date.now() + 3600000); // 1 hour

    user.passwordResetToken = resetToken;
    user.passwordResetExpires = resetExpires;
    await this.userRepository.save(user);

    // Send email with reset link
    try {
      await this.emailService.sendPasswordReset(user.email, resetToken);
    } catch (error) {
      console.error('Failed to send password reset email:', error);
      // Don't throw error to avoid revealing if user exists
    }

    return { message: 'If the email exists, a password reset link has been sent' };
  }

  async updatePassword(updatePasswordDto: UpdatePasswordDto): Promise<{ message: string }> {
    const { token, newPassword } = updatePasswordDto;

    const user = await this.userRepository.findOne({
      where: { 
        passwordResetToken: token,
      },
      select: ['id', 'passwordResetExpires', 'passwordResetToken']
    });

    if (!user || !user.passwordResetExpires || user.passwordResetExpires < new Date()) {
      throw new Error('Invalid or expired reset token');
    }

    // Update password
    user.password = newPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await this.userRepository.save(user);

    // Revoke all refresh tokens for this user
    await this.refreshTokenRepository.update(
      { userId: user.id },
      { isRevoked: true, revokedAt: new Date() }
    );

    return { message: 'Password updated successfully' };
  }

  async verifyEmail(verifyEmailDto: VerifyEmailDto): Promise<{ user: User; message: string }> {
    const { token, workspaceId = 'default' } = verifyEmailDto;

    const user = await this.userRepository.findOne({
      where: { emailVerifyToken: token, workspaceId },
      select: ['id', 'emailVerifyToken', 'emailVerified']
    });

    if (!user) {
      throw new Error('Invalid verification token');
    }

    user.emailVerified = true;
    user.confirmedAt = new Date();
    user.emailVerifyToken = undefined;
    await this.userRepository.save(user);

    return {
      user: user.toSafeObject() as User,
      message: 'Email verified successfully'
    };
  }

  async sendMagicLink(magicLinkDto: MagicLinkDto): Promise<{ message: string }> {
    const { email, workspaceId = 'default', redirectTo } = magicLinkDto;

    let user = await this.userRepository.findOne({
      where: { email, workspaceId }
    });

    if (!user) {
      // Create new user for magic link
      user = this.userRepository.create({
        email,
        workspaceId,
        role: UserRole.AUTHENTICATED,
        authProvider: AuthProvider.EMAIL,
        emailVerified: true,
        confirmedAt: new Date(),
        isActive: true
      });
      await this.userRepository.save(user);
    }

    // Generate magic link token
    const magicToken = this.generateSecureToken();
    const magicExpires = new Date(Date.now() + 3600000); // 1 hour
    
    // Store magic link token with expiration
    user.magicLinkToken = magicToken;
    user.magicLinkExpires = magicExpires;
    await this.userRepository.save(user);
    
    // Send email with magic link
    try {
      await this.emailService.sendMagicLink(email, magicToken, redirectTo);
    } catch (error) {
      console.error('Failed to send magic link email:', error);
      // Don't throw error to avoid revealing if user exists
    }

    return { message: 'Magic link sent to your email' };
  }

  async verifyMagicLink(token: string): Promise<AuthResponse> {
    const user = await this.userRepository.findOne({
      where: { 
        magicLinkToken: token,
      },
      select: ['id', 'email', 'magicLinkToken', 'magicLinkExpires', 'workspaceId', 'role', 'adminRole']
    });

    if (!user || !user.magicLinkExpires || user.magicLinkExpires < new Date()) {
      throw new Error('Invalid or expired magic link token');
    }

    // Clear magic link token
    user.magicLinkToken = undefined;
    user.magicLinkExpires = undefined;
    user.lastSignIn = new Date();
    user.signInCount = (user.signInCount || 0) + 1;
    await this.userRepository.save(user);

    // Generate JWT tokens
    const tokens = await this.generateTokens(user);

    return {
      user: user.toSafeObject() as User,
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: tokens.expiresIn,
      expiresAt: tokens.expiresAt,
      tokenType: 'Bearer'
    };
  }

  async updateUser(userId: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    
    if (!user) {
      throw new Error('User not found');
    }

    // Update fields
    if (updateUserDto.firstName !== undefined) user.firstName = updateUserDto.firstName;
    if (updateUserDto.lastName !== undefined) user.lastName = updateUserDto.lastName;
    if (updateUserDto.email !== undefined) user.email = updateUserDto.email;
    if (updateUserDto.password !== undefined) user.password = updateUserDto.password;
    if (updateUserDto.metadata !== undefined) user.rawUserMetaData = updateUserDto.metadata;

    await this.userRepository.save(user);
    return user.toSafeObject() as User;
  }

  async getUser(userId: string): Promise<User | null> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    return user ? user.toSafeObject() as User : null;
  }

  async signOut(refreshToken: string): Promise<{ message: string }> {
    // Revoke refresh token
    await this.refreshTokenRepository.update(
      { token: refreshToken },
      { isRevoked: true, revokedAt: new Date() }
    );

    return { message: 'Signed out successfully' };
  }

  async signOutAll(userId: string): Promise<{ message: string }> {
    // Revoke all refresh tokens for user
    await this.refreshTokenRepository.update(
      { userId },
      { isRevoked: true, revokedAt: new Date() }
    );

    return { message: 'Signed out from all devices' };
  }

  private async generateTokens(user: User): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    expiresAt: number;
  }> {
    const jwtSecret = (config as any).jwt?.secret || 'your_jwt_secret_key';
    const jwtExpiresIn = (config as any).jwt?.expiresIn || '1h';
    const refreshExpiresIn = (config as any).jwt?.refreshExpiresIn || '7d';

    // Generate access token
    const payload = user.getJwtPayload();
    const accessToken = jwt.sign(payload, jwtSecret, { expiresIn: jwtExpiresIn });

    // Calculate expiration times
    const expiresIn = 3600; // 1 hour in seconds
    const expiresAt = Math.floor(Date.now() / 1000) + expiresIn;

    // Generate refresh token
    const refreshTokenEntity = this.refreshTokenRepository.create({
      userId: user.id,
      workspaceId: user.workspaceId,
      expiresAt: new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)) // 7 days
    });

    await this.refreshTokenRepository.save(refreshTokenEntity);

    return {
      accessToken,
      refreshToken: refreshTokenEntity.token,
      expiresIn,
      expiresAt
    };
  }

  private generateSecureToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  // JWT utilities
  verifyAccessToken(token: string): any {
    try {
      const jwtSecret = (config as any).jwt?.secret || 'your_jwt_secret_key';
      return jwt.verify(token, jwtSecret);
    } catch (error) {
      throw new Error('Invalid access token');
    }
  }

  // Session management
  async createSession(sessionDto: CreateSessionDto): Promise<Session> {
    const sessionId = uuidv4();
    const { accessToken, refreshToken } = await this.generateTokens(
      await this.userRepository.findOne({ where: { id: sessionDto.userId } }) as User
    );

    const session: Session = {
      id: sessionId,
      userId: sessionDto.userId,
      workspaceId: sessionDto.workspaceId,
      accessToken,
      refreshToken,
      expiresAt: new Date(Date.now() + 3600000), // 1 hour
      ipAddress: sessionDto.ipAddress,
      userAgent: sessionDto.userAgent,
      isActive: true,
      createdAt: new Date(),
      lastAccessedAt: new Date()
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  async getSession(sessionId: string): Promise<Session | null> {
    return this.sessions.get(sessionId) || null;
  }

  async revokeSession(sessionId: string): Promise<void> {
    this.sessions.delete(sessionId);
  }

  async getUserSessions(userId: string): Promise<Session[]> {
    return Array.from(this.sessions.values()).filter(session => session.userId === userId);
  }

  async getUserById(userId: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { id: userId }
    });
  }

  async getUserByEmail(email: string, workspaceId?: string): Promise<User | null> {
    const whereClause: any = { email };
    if (workspaceId) {
      whereClause.workspaceId = workspaceId;
    }
    
    return await this.userRepository.findOne({
      where: whereClause
    });
  }

  // OAuth Methods
  getOAuthAuthUrl(provider: AuthProvider, state?: string): string {
    try {
      return this.oauthService.getAuthUrl(provider, state);
    } catch (error: any) {
      throw new Error(`Failed to generate OAuth URL for ${provider}: ${error.message}`);
    }
  }

  async signInWithOAuth(oauthDto: OAuthDto): Promise<AuthResponse> {
    const { provider, code, state, workspaceId = 'default' } = oauthDto;

    try {
      // Validate provider is enabled
      if (!this.oauthService.isProviderEnabled(provider)) {
        throw new Error(`OAuth provider ${provider} is not enabled`);
      }

      // Exchange code for access token
      const accessToken = await this.oauthService.exchangeCodeForToken(provider, code);

      // Get user info from OAuth provider
      const oauthUserInfo = await this.oauthService.getUserInfo(provider, accessToken);

      // Find or create user
      let user = await this.findOrCreateOAuthUser(oauthUserInfo, workspaceId);

      // Update user with latest OAuth info
      user = await this.updateUserOAuthInfo(user, oauthUserInfo);

      // Generate tokens
      const { accessToken: authToken, refreshToken } = await this.generateTokens(user);

      // Create session
      await this.createSession({
        userId: user.id,
        workspaceId,
        ipAddress: undefined,
        userAgent: undefined
      });

      return {
        user,
        accessToken: authToken,
        refreshToken,
        expiresIn: 3600,
        expiresAt: Date.now() + 3600000,
        tokenType: 'Bearer'
      };
    } catch (error: any) {
      console.error(`OAuth sign-in failed for ${provider}:`, error.message);
      throw new Error(`OAuth authentication failed: ${error.message}`);
    }
  }

  private async findOrCreateOAuthUser(oauthUserInfo: OAuthUserInfo, workspaceId: string): Promise<User> {
    // First, try to find user by email
    let user = await this.userRepository.findOne({
      where: { email: oauthUserInfo.email, workspaceId }
    });

    if (user) {
      // User exists, update OAuth provider info if needed
      if (!user.oauthProviders) {
        user.oauthProviders = {};
      }
      user.oauthProviders[oauthUserInfo.provider] = {
        providerId: oauthUserInfo.providerId,
        lastSignIn: new Date()
      };
      return await this.userRepository.save(user);
    }

    // Create new user
    user = this.userRepository.create({
      email: oauthUserInfo.email,
      firstName: oauthUserInfo.firstName,
      lastName: oauthUserInfo.lastName,
      workspaceId,
      provider: oauthUserInfo.provider,
      isEmailVerified: true, // OAuth emails are considered verified
      oauthProviders: {
        [oauthUserInfo.provider]: {
          providerId: oauthUserInfo.providerId,
          lastSignIn: new Date()
        }
      },
      metadata: {
        ...oauthUserInfo.metadata,
        avatar: oauthUserInfo.avatar,
        signUpMethod: 'oauth',
        oauthProvider: oauthUserInfo.provider
      },
      role: UserRole.AUTHENTICATED
    });

    return await this.userRepository.save(user);
  }

  private async updateUserOAuthInfo(user: User, oauthUserInfo: OAuthUserInfo): Promise<User> {
    // Update user info with latest from OAuth provider
    if (oauthUserInfo.name && !user.firstName && !user.lastName) {
      user.firstName = oauthUserInfo.firstName;
      user.lastName = oauthUserInfo.lastName;
    }

    if (oauthUserInfo.avatar) {
      if (!user.metadata) user.metadata = {};
      user.metadata.avatar = oauthUserInfo.avatar;
    }

    // Update OAuth provider info
    if (!user.oauthProviders) {
      user.oauthProviders = {};
    }
    user.oauthProviders[oauthUserInfo.provider] = {
      providerId: oauthUserInfo.providerId,
      lastSignIn: new Date()
    };

    user.lastSignInAt = new Date();

    return await this.userRepository.save(user);
  }

  async linkOAuthProvider(userId: string, oauthDto: OAuthDto): Promise<{ message: string; user: User }> {
    const { provider, code } = oauthDto;

    try {
      // Get current user
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new Error('User not found');
      }

      // Exchange code for access token
      const accessToken = await this.oauthService.exchangeCodeForToken(provider, code);

      // Get user info from OAuth provider
      const oauthUserInfo = await this.oauthService.getUserInfo(provider, accessToken);

      // Check if this OAuth account is already linked to another user
      const existingUser = await this.userRepository.findOne({
        where: { email: oauthUserInfo.email, workspaceId: user.workspaceId }
      });

      if (existingUser && existingUser.id !== userId) {
        throw new Error('This OAuth account is already linked to another user');
      }

      // Link OAuth provider to current user
      if (!user.oauthProviders) {
        user.oauthProviders = {};
      }
      user.oauthProviders[provider] = {
        providerId: oauthUserInfo.providerId,
        lastSignIn: new Date()
      };

      const updatedUser = await this.userRepository.save(user);

      return {
        message: `Successfully linked ${provider} account`,
        user: updatedUser
      };
    } catch (error: any) {
      console.error(`Failed to link OAuth provider ${provider}:`, error.message);
      throw new Error(`Failed to link OAuth account: ${error.message}`);
    }
  }

  async unlinkOAuthProvider(userId: string, provider: AuthProvider): Promise<{ message: string; user: User }> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.oauthProviders || !user.oauthProviders[provider]) {
        throw new Error(`${provider} account is not linked to this user`);
      }

      // Check if user has password or other OAuth providers
      const hasPassword = !!user.password;
      const hasOtherProviders = Object.keys(user.oauthProviders).length > 1;

      if (!hasPassword && !hasOtherProviders) {
        throw new Error('Cannot unlink the only authentication method. Please set a password first.');
      }

      // Remove OAuth provider
      delete user.oauthProviders[provider];
      const updatedUser = await this.userRepository.save(user);

      return {
        message: `Successfully unlinked ${provider} account`,
        user: updatedUser
      };
    } catch (error: any) {
      console.error(`Failed to unlink OAuth provider ${provider}:`, error.message);
      throw error;
    }
  }

  getEnabledOAuthProviders(): AuthProvider[] {
    return this.oauthService.getEnabledProviders();
  }

  generateOAuthState(): string {
    return this.oauthService.generateState();
  }
}
