import nodemailer from 'nodemailer';
import config from 'config';

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private fromEmail: string;
  private baseUrl: string;

  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.baseUrl = process.env.BASE_URL || 'http://localhost:3333';
    
    // Configure transporter based on environment
    if (process.env.NODE_ENV === 'production') {
      this.transporter = this.createProductionTransporter();
    } else {
      this.transporter = this.createDevelopmentTransporter();
    }
  }

  /**
   * Create production email transporter (SMTP)
   */
  private createProductionTransporter(): nodemailer.Transporter {
    const emailConfig: EmailConfig = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || ''
      }
    };

    console.log('📧 Email service configured for production with SMTP');
    return nodemailer.createTransporter(emailConfig);
  }

  /**
   * Create development email transporter (logs to console)
   */
  private createDevelopmentTransporter(): nodemailer.Transporter {
    console.log('📧 Email service configured for development (console logging)');
    
    // For development, create a test account with Ethereal Email
    return nodemailer.createTransporter({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>', // Mock user
        pass: 'ethereal.pass' // Mock password
      }
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(email: string, resetToken: string): Promise<void> {
    const resetUrl = `${this.baseUrl}/auth/reset-password?token=${resetToken}`;
    
    const template = this.getPasswordResetTemplate(resetUrl);
    
    await this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text
    });

    console.log(`📧 Password reset email sent to ${email}`);
  }

  /**
   * Send magic link email
   */
  async sendMagicLink(email: string, magicToken: string, redirectTo?: string): Promise<void> {
    let magicUrl = `${this.baseUrl}/auth/magic-link?token=${magicToken}`;
    if (redirectTo) {
      magicUrl += `&redirect_to=${encodeURIComponent(redirectTo)}`;
    }
    
    const template = this.getMagicLinkTemplate(magicUrl);
    
    await this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text
    });

    console.log(`📧 Magic link email sent to ${email}`);
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(email: string, verificationToken: string): Promise<void> {
    const verificationUrl = `${this.baseUrl}/auth/verify-email?token=${verificationToken}`;
    
    const template = this.getEmailVerificationTemplate(verificationUrl);
    
    await this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text
    });

    console.log(`📧 Email verification sent to ${email}`);
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(email: string, firstName?: string): Promise<void> {
    const template = this.getWelcomeTemplate(firstName);
    
    await this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text
    });

    console.log(`📧 Welcome email sent to ${email}`);
  }

  /**
   * Generic email sending method
   */
  private async sendEmail(options: {
    to: string;
    subject: string;
    html: string;
    text: string;
  }): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {
        // In development, log email to console instead of sending
        console.log('\n' + '='.repeat(60));
        console.log('📧 EMAIL (Development Mode)');
        console.log('='.repeat(60));
        console.log(`From: ${this.fromEmail}`);
        console.log(`To: ${options.to}`);
        console.log(`Subject: ${options.subject}`);
        console.log('---');
        console.log('TEXT VERSION:');
        console.log(options.text);
        console.log('---');
        console.log('HTML VERSION:');
        console.log(options.html);
        console.log('='.repeat(60) + '\n');
        return;
      }

      // Send actual email in production
      const info = await this.transporter.sendMail({
        from: this.fromEmail,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html
      });

      console.log(`📧 Email sent: ${info.messageId}`);
    } catch (error) {
      console.error('❌ Failed to send email:', error);
      throw new Error(`Failed to send email: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Password reset email template
   */
  private getPasswordResetTemplate(resetUrl: string): EmailTemplate {
    return {
      subject: 'Reset your E-BaaS password',
      text: `
Password Reset Request

Hi there,

You requested to reset your password for your E-BaaS account.

Click the link below to reset your password:
${resetUrl}

This link will expire in 1 hour.

If you didn't request this, please ignore this email.

Best regards,
The E-BaaS Team
      `.trim(),
      html: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">E-BaaS</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Backend as a Service</p>
          </div>
          
          <div style="padding: 40px 30px; background: white;">
            <h2 style="color: #333; margin-bottom: 20px;">Password Reset Request</h2>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
              Hi there,<br><br>
              You requested to reset your password for your E-BaaS account.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 5px; 
                        font-weight: bold;
                        display: inline-block;">
                Reset Password
              </a>
            </div>
            
            <p style="color: #999; font-size: 14px; line-height: 1.5;">
              This link will expire in 1 hour.<br>
              If you didn't request this, please ignore this email.
            </p>
          </div>
          
          <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>Best regards,<br>The E-BaaS Team</p>
          </div>
        </div>
      `
    };
  }

  /**
   * Magic link email template
   */
  private getMagicLinkTemplate(magicUrl: string): EmailTemplate {
    return {
      subject: 'Your E-BaaS magic link',
      text: `
Magic Link Sign In

Hi there,

Click the link below to sign in to your E-BaaS account:
${magicUrl}

This link will expire in 1 hour.

If you didn't request this, please ignore this email.

Best regards,
The E-BaaS Team
      `.trim(),
      html: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">E-BaaS</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Backend as a Service</p>
          </div>
          
          <div style="padding: 40px 30px; background: white;">
            <h2 style="color: #333; margin-bottom: 20px;">Magic Link Sign In</h2>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
              Hi there,<br><br>
              Click the button below to sign in to your E-BaaS account.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${magicUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 5px; 
                        font-weight: bold;
                        display: inline-block;">
                Sign In with Magic Link
              </a>
            </div>
            
            <p style="color: #999; font-size: 14px; line-height: 1.5;">
              This link will expire in 1 hour.<br>
              If you didn't request this, please ignore this email.
            </p>
          </div>
          
          <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>Best regards,<br>The E-BaaS Team</p>
          </div>
        </div>
      `
    };
  }

  /**
   * Email verification template
   */
  private getEmailVerificationTemplate(verificationUrl: string): EmailTemplate {
    return {
      subject: 'Verify your E-BaaS email address',
      text: `
Email Verification

Hi there,

Please verify your email address for your E-BaaS account:
${verificationUrl}

This link will expire in 24 hours.

Best regards,
The E-BaaS Team
      `.trim(),
      html: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">E-BaaS</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Backend as a Service</p>
          </div>
          
          <div style="padding: 40px 30px; background: white;">
            <h2 style="color: #333; margin-bottom: 20px;">Verify Your Email</h2>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
              Hi there,<br><br>
              Please verify your email address to complete your E-BaaS account setup.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 5px; 
                        font-weight: bold;
                        display: inline-block;">
                Verify Email Address
              </a>
            </div>
            
            <p style="color: #999; font-size: 14px; line-height: 1.5;">
              This link will expire in 24 hours.
            </p>
          </div>
          
          <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>Best regards,<br>The E-BaaS Team</p>
          </div>
        </div>
      `
    };
  }

  /**
   * Welcome email template
   */
  private getWelcomeTemplate(firstName?: string): EmailTemplate {
    const greeting = firstName ? `Hi ${firstName}` : 'Hi there';
    
    return {
      subject: 'Welcome to E-BaaS!',
      text: `
Welcome to E-BaaS!

${greeting},

Welcome to E-BaaS - your Backend as a Service platform!

You now have access to:
• Database management
• Authentication system
• Storage & CDN
• Real-time features
• Edge functions
• Queue system

Get started by exploring our documentation and building your first app.

Best regards,
The E-BaaS Team
      `.trim(),
      html: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">E-BaaS</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Backend as a Service</p>
          </div>
          
          <div style="padding: 40px 30px; background: white;">
            <h2 style="color: #333; margin-bottom: 20px;">Welcome to E-BaaS!</h2>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
              ${greeting},<br><br>
              Welcome to E-BaaS - your Backend as a Service platform!
            </p>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 30px 0;">
              <h3 style="color: #333; margin-top: 0;">You now have access to:</h3>
              <ul style="color: #666; line-height: 1.8; padding-left: 20px;">
                <li>🗄️ Database management</li>
                <li>🔐 Authentication system</li>
                <li>📁 Storage & CDN</li>
                <li>⚡ Real-time features</li>
                <li>🚀 Edge functions</li>
                <li>🔄 Queue system</li>
              </ul>
            </div>
            
            <p style="color: #666; line-height: 1.6;">
              Get started by exploring our documentation and building your first app.
            </p>
          </div>
          
          <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>Best regards,<br>The E-BaaS Team</p>
          </div>
        </div>
      `
    };
  }

  /**
   * Verify email service configuration
   */
  async verifyConfiguration(): Promise<boolean> {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('📧 Email service: Development mode (console logging)');
        return true;
      }

      await this.transporter.verify();
      console.log('📧 Email service: SMTP connection verified');
      return true;
    } catch (error) {
      console.error('❌ Email service configuration error:', error);
      return false;
    }
  }
}