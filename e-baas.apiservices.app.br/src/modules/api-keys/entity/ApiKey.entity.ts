import "reflect-metadata";
import {
  <PERSON><PERSON><PERSON>rt,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { v4 as uuidv4 } from "uuid";
import crypto from "crypto";
import config from "../../../infra/config";
import { ApiKeyType, ApiKeyScope } from "../dto/api-key.dto";

@Entity("api_keys")
@Index(["apiKey"], { unique: true })
@Index(["workspaceId", "type"])
@Index(["workspaceId", "isActive"])
export class ApiKey {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 100 })
  name: string;

  @Column({ name: "api_key", type: "varchar", length: 500, unique: true, select: false })
  apiKey: string;

  @Column({ name: "key_prefix", type: "varchar", length: 20 })
  keyPrefix: string; // First 8 chars for identification

  @Column({ name: "workspace_id", type: "varchar", length: 255 })
  workspaceId: string;

  @Column({ 
    type: "enum", 
    enum: ApiKeyType,
    default: ApiKeyType.AUTHENTICATED 
  })
  type: ApiKeyType;

  @Column({ 
    type: "simple-array",
    default: ApiKeyScope.ALL 
  })
  scopes: ApiKeyScope[];

  @Column({ name: "is_active", type: "boolean", default: true })
  isActive: boolean;

  @Column({
    name: "expires_at",
    type: process.env.NODE_ENV === "test" ? "datetime" : "timestamp",
    nullable: true,
  })
  expiresAt?: Date;

  @Column({ type: "text", nullable: true })
  description?: string;

  @Column({ name: "rate_limit", type: "integer", nullable: true })
  rateLimit?: number; // requests per minute

  @Column({ 
    name: "allowed_origins", 
    type: "simple-array", 
    nullable: true 
  })
  allowedOrigins?: string[];

  @Column({ 
    name: "allowed_ips", 
    type: "simple-array", 
    nullable: true 
  })
  allowedIps?: string[];

  @Column({ name: "last_used_at", type: "timestamp", nullable: true })
  lastUsedAt?: Date;

  @Column({ name: "usage_count", type: "integer", default: 0 })
  usageCount: number;

  @Column({ name: "created_by", type: "varchar", length: 255, nullable: true })
  createdBy?: string; // User ID who created the key

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @ManyToOne("Workspace", "apiKeys")
  @JoinColumn({ name: "workspace_id" })
  workspace?: any;

  @BeforeInsert()
  generateApiKey() {
    const keyPrefix = config.api.keyPrefix || 'ebaas_';
    const typePrefix = this.getTypePrefix();
    const randomBytes = crypto.randomBytes(32).toString('hex');
    
    this.apiKey = `${keyPrefix}${typePrefix}_${randomBytes}`;
    this.keyPrefix = this.apiKey.substring(0, 20);
  }

  @BeforeUpdate()
  updateUsage() {
    if (this.lastUsedAt) {
      this.usageCount += 1;
    }
  }

  private getTypePrefix(): string {
    switch (this.type) {
      case ApiKeyType.ANON:
        return 'anon';
      case ApiKeyType.SERVICE_ROLE:
        return 'svc';
      case ApiKeyType.AUTHENTICATED:
        return 'auth';
      default:
        return 'key';
    }
  }

  // Check if API key is expired
  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  // Check if API key is valid (active and not expired)
  isValid(): boolean {
    return this.isActive && !this.isExpired();
  }

  // Check if key has specific scope
  hasScope(scope: ApiKeyScope): boolean {
    return this.scopes.includes(ApiKeyScope.ALL) || this.scopes.includes(scope);
  }

  // Check if IP is allowed
  isIpAllowed(ipAddress: string): boolean {
    if (!this.allowedIps || this.allowedIps.length === 0) return true;
    return this.allowedIps.includes(ipAddress);
  }

  // Check if origin is allowed
  isOriginAllowed(origin: string): boolean {
    if (!this.allowedOrigins || this.allowedOrigins.length === 0) return true;
    return this.allowedOrigins.some(allowedOrigin => {
      if (allowedOrigin === '*') return true;
      if (allowedOrigin.includes('*')) {
        const regex = new RegExp(allowedOrigin.replace(/\*/g, '.*'));
        return regex.test(origin);
      }
      return allowedOrigin === origin;
    });
  }

  // Update last used timestamp
  markAsUsed(): void {
    this.lastUsedAt = new Date();
  }

  // Get safe object (without secret)
  toSafeObject() {
    const { apiKey, ...safeKey } = this;
    return {
      ...safeKey,
      prefix: this.keyPrefix
    };
  }

  // Get remaining time until expiration in milliseconds
  getRemainingTime(): number | null {
    if (!this.expiresAt) return null;
    return Math.max(0, this.expiresAt.getTime() - Date.now());
  }

  // Check if key is close to expiry (within 7 days)
  isNearExpiry(): boolean {
    if (!this.expiresAt) return false;
    const sevenDays = 7 * 24 * 60 * 60 * 1000;
    return (this.expiresAt.getTime() - Date.now()) < sevenDays;
  }
}
