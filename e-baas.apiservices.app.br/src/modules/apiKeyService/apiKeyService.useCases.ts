import { ApiKeyService } from "./entity/api-key-service.entity";
import { api-key-serviceRepository } from "infra/repository";
import IContractUseCases from "infra/contracts";
import { <PERSON>rrorHandler } from "infra/errorHandlers";

export default class ApiKeyServiceUseCases implements IContractUseCases<ApiKeyService> {
    constructor() {}

    async getAll(): Promise<ApiKeyService[]> {
        try {
            return await api-key-serviceRepository.find();
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async getOne(id: string): Promise<ApiKeyService> {
        try {
            const api-key-service = await api-key-serviceRepository.findOneBy({ id });
            if (!api-key-service) {
                throw ErrorHandler.NotFound("ApiKeyService not found");
            }
            return api-key-service;
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "ApiKeyService not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async create(data: Partial<ApiKeyService>): Promise<ApiKeyService> {
        try {
            return await api-key-serviceRepository.save(data);
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async update(id: string, data: Partial<ApiKeyService>): Promise<ApiKeyService> {
        try {
            const api-key-service = await this.getOne(id);
            await api-key-serviceRepository.update(id, data);
            return { ...api-key-service, ...data };
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "ApiKeyService not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await this.getOne(id);
            await api-key-serviceRepository.delete(id);
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "ApiKeyService not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }
}