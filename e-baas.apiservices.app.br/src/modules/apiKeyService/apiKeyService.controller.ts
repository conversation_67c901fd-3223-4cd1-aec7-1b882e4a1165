import { Router } from "express";
import { validator } from "infra";

import ApiKeyServiceService from "modules/api-key-service/api-key-service.service";
import ApiKeyServiceUseCases from "modules/api-key-service/api-key-service.useCases"; 
import { ApiKeyServiceDto } from "modules/api-key-service/dto/api-key-service.dto";

const controller = Router();
const api-key-serviceUseCases = new ApiKeyServiceUseCases(); 
const api-key-serviceService = new ApiKeyServiceService(api-key-serviceUseCases);

controller.get("/", (req, res) => api-key-serviceService.getAll(req, res));
controller.get("/:id", (req, res) => api-key-serviceService.getOne(req, res));
controller.post("/", validator(ApiKeyServiceDto), (req, res) => api-key-serviceService.create(req, res));
controller.put("/:id", validator(ApiKeyServiceDto), (req, res) => api-key-serviceService.update(req, res));
controller.delete("/:id", (req, res) => api-key-serviceService.delete(req, res));

export default controller;