import { Repository } from "typeorm";
import { AppDataSource } from "../../infra/database/data-source";
import { User } from "../users/entity/user.entity";
import { Workspace } from "../workspaces/entity/Workspace.entity";
import { Api<PERSON>ey } from "../api-keys/entity/ApiKey.entity";
import { AssignAdminRoleDto } from "../auth/dto/admin-roles.dto";

interface PaginationOptions {
  page: number;
  limit: number;
  search?: string;
}

interface CreateApiKeyOptions {
  name: string;
  permissions?: string[];
  expiresAt?: Date;
  createdBy?: string;
}

export class AdminService {
  private userRepository: Repository<User>;
  private workspaceRepository: Repository<Workspace>;
  private apiKeyRepository: Repository<ApiKey>;

  constructor() {
    this.userRepository = AppDataSource.getRepository(User);
    this.workspaceRepository = AppDataSource.getRepository(Workspace);
    this.apiKeyRepository = AppDataSource.getRepository(ApiKey);
  }

  // ==== PLATFORM ADMIN METHODS ====

  async getAllWorkspaces() {
    const workspaces = await this.workspaceRepository.find({
      relations: ['owner'],
      order: { createdAt: 'DESC' }
    });

    return workspaces.map(workspace => ({
      id: workspace.id,
      name: workspace.name,
      owner: {
        id: workspace.owner.id,
        email: workspace.owner.email,
        fullName: workspace.owner.fullName
      },
      createdAt: workspace.createdAt,
      updatedAt: workspace.updatedAt,
      isActive: workspace.isActive,
      // Add metrics
      userCount: await this.userRepository.count({ where: { workspaceId: workspace.id } }),
      storageUsage: await this.calculateWorkspaceStorageUsage(workspace.id),
      apiCallsThisMonth: await this.calculateApiCallsThisMonth(workspace.id)
    }));
  }

  async getGlobalAnalytics(period: string) {
    const totalWorkspaces = await this.workspaceRepository.count();
    const totalUsers = await this.userRepository.count();
    const activeWorkspaces = await this.workspaceRepository.count({ where: { isActive: true } });
    const verifiedUsers = await this.userRepository.count({ where: { emailVerified: true } });

    // Calculate growth (comparing with previous period)
    const previousPeriodStart = this.getPreviousPeriodDate(period);
    const currentPeriodStart = this.getCurrentPeriodDate(period);
    
    const previousWorkspaces = await this.workspaceRepository
      .createQueryBuilder('workspace')
      .where('workspace.createdAt < :date', { date: previousPeriodStart })
      .getCount();
    const previousUsers = await this.userRepository
      .createQueryBuilder('user')
      .where('user.createdAt < :date', { date: previousPeriodStart })
      .getCount();

    const workspaceGrowth = previousWorkspaces > 0 
      ? ((totalWorkspaces - previousWorkspaces) / previousWorkspaces) * 100 
      : 0;
    const userGrowth = previousUsers > 0 
      ? ((totalUsers - previousUsers) / previousUsers) * 100 
      : 0;

    // API calls metrics (simplified - in real implementation, would query logs/metrics table)
    const apiCalls = await this.getApiCallsMetrics(period);
    const storageMetrics = await this.getStorageMetrics();

    return {
      period,
      workspaces: {
        total: totalWorkspaces,
        active: activeWorkspaces,
        growth: Math.round(workspaceGrowth * 100) / 100
      },
      users: {
        total: totalUsers,
        verified: verifiedUsers,
        growth: Math.round(userGrowth * 100) / 100
      },
      apiCalls,
      storage: storageMetrics
    };
  }

  async assignAdminRole(assignDto: AssignAdminRoleDto) {
    const user = await this.userRepository.findOne({ 
      where: { id: assignDto.userId } 
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Validate workspace exists if workspace-specific role
    if (assignDto.workspaceId && ['workspace_admin', 'workspace_owner'].includes(assignDto.adminRole)) {
      const workspace = await this.workspaceRepository.findOne({
        where: { id: assignDto.workspaceId }
      });

      if (!workspace) {
        throw new Error('Workspace not found');
      }

      // If assigning workspace owner, check current owner
      if (assignDto.adminRole === 'workspace_owner' && workspace.ownerId !== user.id) {
        // Transfer ownership
        workspace.ownerId = user.id;
        await this.workspaceRepository.save(workspace);
      }
    }

    // Update user admin role
    user.adminRole = assignDto.adminRole;
    if (assignDto.workspaceId) {
      user.workspaceId = assignDto.workspaceId;
    }

    await this.userRepository.save(user);

    return {
      message: 'Admin role assigned successfully',
      user: {
        id: user.id,
        email: user.email,
        adminRole: user.adminRole,
        workspaceId: user.workspaceId,
        permissions: user.getPermissions()
      }
    };
  }

  async getAllUsers(options: PaginationOptions) {
    const { page, limit, search } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.userRepository.createQueryBuilder('user')
      .leftJoinAndSelect('user.workspaces', 'workspace')
      .select([
        'user.id',
        'user.email',
        'user.firstName',
        'user.lastName',
        'user.role',
        'user.adminRole',
        'user.workspaceId',
        'user.provider',
        'user.isActive',
        'user.createdAt',
        'user.lastSignIn',
        'workspace.id',
        'workspace.name'
      ])
      .orderBy('user.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    if (search) {
      queryBuilder.where(
        'user.email ILIKE :search OR user.firstName ILIKE :search OR user.lastName ILIKE :search',
        { search: `%${search}%` }
      );
    }

    const [users, total] = await queryBuilder.getManyAndCount();

    return {
      users: users.map(user => ({
        ...user,
        fullName: user.fullName,
        permissions: user.getPermissions(),
        canAccessAdmin: user.canAccessAdmin
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // ==== WORKSPACE ADMIN METHODS ====

  async getWorkspaceDetails(workspaceId: string) {
    const workspace = await this.workspaceRepository.findOne({
      where: { id: workspaceId },
      relations: ['owner']
    });

    if (!workspace) {
      throw new Error('Workspace not found');
    }

    const userCount = await this.userRepository.count({ 
      where: { workspaceId } 
    });

    const adminCount = await this.userRepository.count({
      where: { 
        workspaceId,
        adminRole: 'workspace_admin'
      }
    });

    return {
      id: workspace.id,
      name: workspace.name,
      description: workspace.description,
      owner: {
        id: workspace.owner.id,
        email: workspace.owner.email,
        fullName: workspace.owner.fullName
      },
      createdAt: workspace.createdAt,
      updatedAt: workspace.updatedAt,
      isActive: workspace.isActive,
      stats: {
        userCount,
        adminCount,
        storageUsage: await this.calculateWorkspaceStorageUsage(workspaceId),
        apiCallsThisMonth: await this.calculateApiCallsThisMonth(workspaceId)
      }
    };
  }

  async getWorkspaceUsers(workspaceId: string, options: PaginationOptions) {
    const { page, limit, search } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.userRepository.createQueryBuilder('user')
      .where('user.workspaceId = :workspaceId', { workspaceId })
      .select([
        'user.id',
        'user.email',
        'user.firstName',
        'user.lastName',
        'user.role',
        'user.adminRole',
        'user.provider',
        'user.isActive',
        'user.createdAt',
        'user.lastSignIn'
      ])
      .orderBy('user.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    if (search) {
      queryBuilder.andWhere(
        'user.email ILIKE :search OR user.firstName ILIKE :search OR user.lastName ILIKE :search',
        { search: `%${search}%` }
      );
    }

    const [users, total] = await queryBuilder.getManyAndCount();

    return {
      users: users.map(user => ({
        ...user,
        fullName: user.fullName,
        permissions: user.getPermissions(),
        canAccessAdmin: user.canAccessAdmin
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async getWorkspaceAnalytics(workspaceId: string, period: string) {
    const userCount = await this.userRepository.count({ 
      where: { workspaceId } 
    });
    
    const activeUsers = await this.userRepository
      .createQueryBuilder('user')
      .where('user.workspaceId = :workspaceId', { workspaceId })
      .andWhere('user.lastSignIn >= :date', { date: this.getCurrentPeriodDate(period) })
      .getCount();

    // Calculate user growth for workspace
    const previousPeriodStart = this.getPreviousPeriodDate(period);
    const previousUserCount = await this.userRepository
      .createQueryBuilder('user')
      .where('user.workspaceId = :workspaceId', { workspaceId })
      .andWhere('user.createdAt < :date', { date: previousPeriodStart })
      .getCount();

    const userGrowth = previousUserCount > 0 
      ? ((userCount - previousUserCount) / previousUserCount) * 100 
      : 0;

    // API calls metrics for workspace
    const apiCalls = await this.getApiCallsMetrics(period, workspaceId);
    
    // Storage metrics for workspace
    const storageMetrics = await this.getStorageMetrics(workspaceId);
    
    // Database metrics for workspace
    const databaseMetrics = await this.getDatabaseMetrics(workspaceId);

    return {
      period,
      workspaceId,
      users: {
        total: userCount,
        active: activeUsers,
        growth: Math.round(userGrowth * 100) / 100
      },
      apiCalls,
      storage: storageMetrics,
      database: databaseMetrics
    };
  }

  async getWorkspaceApiKeys(workspaceId: string) {
    const apiKeys = await this.apiKeyRepository.find({
      where: { workspaceId },
      select: ['id', 'name', 'permissions', 'expiresAt', 'createdAt', 'lastUsedAt', 'isActive']
    });

    return apiKeys;
  }

  async createWorkspaceApiKey(workspaceId: string, options: CreateApiKeyOptions) {
    // TODO: Implement API key creation
    // This would integrate with the existing API key service
    return {
      message: 'API key creation not yet implemented',
      workspaceId,
      options
    };
  }

  async getWorkspaceDatabaseSchemas(workspaceId: string) {
    // TODO: Implement database schema retrieval for workspace
    return {
      schemas: [],
      tables: [],
      message: 'Database schema retrieval not yet implemented'
    };
  }

  async getWorkspaceStorageUsage(workspaceId: string) {
    const storageMetrics = await this.getStorageMetrics(workspaceId);
    
    return {
      totalSize: storageMetrics.usage || 0,
      fileCount: storageMetrics.files || 0,
      bucketCount: 0, // TODO: implement bucket counting
      quotaUsed: storageMetrics.usage || 0,
      quotaLimit: 5368709120, // 5GB default
      buckets: [] // TODO: implement bucket listing
    };
  }

  // Helper methods for analytics calculations
  private getPreviousPeriodDate(period: string): Date {
    const now = new Date();
    switch (period) {
      case '1h':
        return new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2 hours ago
      case '24h':
        return new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000); // 2 days ago
      case '7d':
        return new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000); // 14 days ago
      case '30d':
        return new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000); // 60 days ago
      default:
        return new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000); // 60 days ago
    }
  }

  private getCurrentPeriodDate(period: string): Date {
    const now = new Date();
    switch (period) {
      case '1h':
        return new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago
      case '24h':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000); // 1 day ago
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      default:
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    }
  }

  private async getApiCallsMetrics(period: string, workspaceId?: string) {
    // Mock implementation - in real world, this would query a metrics/logs table
    const multiplier = workspaceId ? 1000 : 10000; // Platform vs workspace scale
    
    return {
      total: Math.floor(Math.random() * multiplier) + multiplier,
      successful: Math.floor(Math.random() * multiplier * 0.95) + multiplier * 0.95,
      errors: Math.floor(Math.random() * multiplier * 0.05)
    };
  }

  private async getStorageMetrics(workspaceId?: string) {
    // Mock implementation - in real world, this would query storage tables
    const multiplier = workspaceId ? 1 : 100; // Platform vs workspace scale
    
    return {
      totalUsage: Math.floor(Math.random() * 1073741824 * multiplier), // Random GB
      totalFiles: Math.floor(Math.random() * 1000 * multiplier),
      usage: Math.floor(Math.random() * 1073741824 * multiplier),
      files: Math.floor(Math.random() * 1000 * multiplier)
    };
  }

  private async getDatabaseMetrics(workspaceId: string) {
    // Mock implementation - in real world, this would query information_schema
    return {
      tables: Math.floor(Math.random() * 20) + 5,
      records: Math.floor(Math.random() * 100000) + 1000
    };
  }

  // Helper methods for real metrics calculation
  private async calculateWorkspaceStorageUsage(workspaceId: string): Promise<number> {
    try {
      // In a real implementation, this would query storage files table
      // For now, return a mock value based on workspace activity
      const userCount = await this.userRepository.count({ where: { workspaceId } });
      const baseUsage = userCount * 1024 * 1024; // 1MB per user base
      const randomVariation = Math.floor(Math.random() * 100 * 1024 * 1024); // Up to 100MB variation
      return baseUsage + randomVariation;
    } catch (error) {
      console.error('Error calculating workspace storage usage:', error);
      return 0;
    }
  }

  private async calculateApiCallsThisMonth(workspaceId: string): Promise<number> {
    try {
      // In a real implementation, this would query API logs table
      // For now, return a mock value based on workspace activity
      const userCount = await this.userRepository.count({ where: { workspaceId } });
      const baseCalls = userCount * 1000; // 1000 calls per user base
      const randomVariation = Math.floor(Math.random() * 5000); // Up to 5000 variation
      return baseCalls + randomVariation;
    } catch (error) {
      console.error('Error calculating API calls this month:', error);
      return 0;
    }
  }
}