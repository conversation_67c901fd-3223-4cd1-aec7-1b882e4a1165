import { Router, Request, Response } from "express";
import { RealtimeService } from "./realtime.service";
import {
  SubscribeChannelDto,
  UnsubscribeChannelDto,
  BroadcastMessageDto,
  PresenceUpdateDto,
  CreateChannelDto
} from "./dto/realtime.dto";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";
import { requireReadAccess, requireWriteAccess } from "../../infra/middlewares/apiKeyAuth.middleware";

const realtimeRouter = Router();
const realtimeService = new RealtimeService();

// Helper function for validation
const validateDto = async (DtoClass: any, data: any) => {
  const dto = plainToClass(DtoClass, data);
  const errors = await validate(dto);
  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
  }
  return dto;
};

// CHANNEL MANAGEMENT

// Create channel
realtimeRouter.post("/channels", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const createChannelDto = await validateDto(CreateChannelDto, req.body);
    const createdBy = (req as any).user?.id;
    
    const channel = await realtimeService.createChannel(createChannelDto, createdBy);
    
    return res.status(201).json(channel.toSafeObject());
  } catch (error: any) {
    if (error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Get channel info
realtimeRouter.get("/channels/:channelName", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { channelName } = req.params;
    const { workspaceId } = req.query as any;
    
    const channel = await realtimeService.getChannel(channelName, workspaceId);
    
    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }
    
    return res.status(200).json(channel.toSafeObject());
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// List channels
realtimeRouter.get("/channels", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { workspaceId, type } = req.query as any;
    
    const channels = await realtimeService.listChannels(workspaceId, type);
    
    return res.status(200).json(channels.map(channel => channel.toSafeObject()));
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Delete channel
realtimeRouter.delete("/channels/:channelName", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const { channelName } = req.params;
    const { workspaceId } = req.query as any;
    
    const result = await realtimeService.deleteChannel(channelName, workspaceId);
    
    return res.status(200).json(result);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// MESSAGE BROADCASTING

// Broadcast message
realtimeRouter.post("/broadcast", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const broadcastDto = await validateDto(BroadcastMessageDto, req.body);
    const senderId = (req as any).user?.id || (req as any).apiKey?.id;
    
    const result = await realtimeService.broadcastMessage(broadcastDto, senderId);
    
    return res.status(200).json(result);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    if (error.message.includes('not allowed')) {
      return res.status(403).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// PRESENCE MANAGEMENT

// Update presence
realtimeRouter.post("/presence", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const presenceDto = await validateDto(PresenceUpdateDto, req.body);
    const clientId = req.headers['x-client-id'] as string || 'unknown';
    
    const presenceState = await realtimeService.updatePresence(presenceDto, clientId);
    
    return res.status(200).json(presenceState);
  } catch (error: any) {
    if (error.message.includes('Invalid presence channel')) {
      return res.status(400).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Get presence state
realtimeRouter.get("/presence/:channelName", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { channelName } = req.params;
    const { workspaceId } = req.query as any;
    
    const presenceState = await realtimeService.getPresenceState(channelName, workspaceId);
    
    return res.status(200).json(presenceState);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// SUBSCRIPTION MANAGEMENT (for testing/debugging)

// Subscribe to channel (REST endpoint for testing)
realtimeRouter.post("/subscribe", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const subscribeDto = await validateDto(SubscribeChannelDto, req.body);
    const clientId = req.headers['x-client-id'] as string || 'rest-client';
    const userId = (req as any).user?.id;
    
    const result = await realtimeService.subscribeToChannel(subscribeDto, clientId, userId);
    
    return res.status(200).json({
      success: result.success,
      channel: result.channel.toSafeObject(),
      clientId
    });
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    if (error.message.includes('Access denied')) {
      return res.status(403).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Unsubscribe from channel (REST endpoint for testing)
realtimeRouter.post("/unsubscribe", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const unsubscribeDto = await validateDto(UnsubscribeChannelDto, req.body);
    const clientId = req.headers['x-client-id'] as string || 'rest-client';
    
    const result = await realtimeService.unsubscribeFromChannel(unsubscribeDto, clientId);
    
    return res.status(200).json(result);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// MONITORING AND STATISTICS

// Get channel statistics
realtimeRouter.get("/stats/channels", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { workspaceId } = req.query as any;
    
    const stats = await realtimeService.getChannelStats(workspaceId);
    
    return res.status(200).json(stats);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get active connections
realtimeRouter.get("/stats/connections", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { workspaceId } = req.query as any;
    
    let connections = realtimeService.getActiveConnections();
    
    if (workspaceId) {
      connections = realtimeService.getConnectionsForWorkspace(workspaceId);
    }
    
    return res.status(200).json({
      totalConnections: connections.length,
      connections: connections.map(conn => ({
        id: conn.id,
        userId: conn.userId,
        workspaceId: conn.workspaceId,
        channelCount: conn.channels.length,
        connectedAt: conn.connectedAt,
        lastActivity: conn.lastActivity
      }))
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// WEBHOOK ENDPOINTS for Database Changes

// Notify database change (internal endpoint)
realtimeRouter.post("/internal/database-change", async (req: Request, res: Response) => {
  try {
    // This endpoint would be called by database triggers or change data capture
    const { table, schema, eventType, old, new: newRecord, columns, workspaceId } = req.body;
    
    // Validate internal access (could be secured with internal API key)
    const internalKey = req.headers['x-internal-key'] as string;
    const expectedKey = process.env.INTERNAL_API_KEY;
    
    if (!expectedKey || internalKey !== expectedKey) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    await realtimeService.notifyDatabaseChange({
      table,
      schema: schema || 'public',
      eventType,
      old,
      new: newRecord,
      columns: columns || [],
      timestamp: new Date(),
      commitTimestamp: new Date()
    }, workspaceId);
    
    return res.status(200).json({ success: true });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Health check
realtimeRouter.get("/health", async (req: Request, res: Response) => {
  const activeConnections = realtimeService.getActiveConnections().length;
  
  return res.status(200).json({
    status: 'healthy',
    service: 'realtime',
    activeConnections,
    timestamp: new Date().toISOString()
  });
});

// WEBSOCKET INFO (for client libraries)

// Get WebSocket connection info
realtimeRouter.get("/connection-info", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { workspaceId } = req.query as any;
    const baseUrl = process.env.BASE_URL || 'http://localhost:3333';
    const wsUrl = baseUrl.replace('http', 'ws');
    
    return res.status(200).json({
      wsUrl: `${wsUrl}/realtime`,
      authRequired: true,
      supportedTransports: ['websocket', 'polling'],
      heartbeatInterval: 30000,
      connectionTimeout: 60000,
      maxChannelsPerConnection: 100,
      workspaceId
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

export default realtimeRouter;