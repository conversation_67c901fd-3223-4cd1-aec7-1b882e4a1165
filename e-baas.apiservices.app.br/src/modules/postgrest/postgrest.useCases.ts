import { Postgrest } from "./entity/postgrest.entity";
import { postgrestRepository } from "infra/repository";
import IContractUseCases from "infra/contracts";
import { <PERSON>rrorHandler } from "infra/errorHandlers";

export default class PostgrestUseCases implements IContractUseCases<Postgrest> {
    constructor() {}

    async getAll(): Promise<Postgrest[]> {
        try {
            return await postgrestRepository.find();
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async getOne(id: string): Promise<Postgrest> {
        try {
            const postgrest = await postgrestRepository.findOneBy({ id });
            if (!postgrest) {
                throw ErrorHandler.NotFound("Postgrest not found");
            }
            return postgrest;
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "Postgrest not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async create(data: Partial<Postgrest>): Promise<Postgrest> {
        try {
            return await postgrestRepository.save(data);
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async update(id: string, data: Partial<Postgrest>): Promise<Postgrest> {
        try {
            const postgrest = await this.getOne(id);
            await postgrestRepository.update(id, data);
            return { ...postgrest, ...data };
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "Postgrest not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await this.getOne(id);
            await postgrestRepository.delete(id);
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "Postgrest not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }
}