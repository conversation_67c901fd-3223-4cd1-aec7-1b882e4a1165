import { SqlExecutor } from "./entity/sql-executor.entity";
import { sqlExecutorRepository } from "infra/repository";
import IContractUseCases from "infra/contracts";
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from "infra/errorHandlers";

export default class SqlExecutorUseCases
  implements IContractUseCases<SqlExecutor>
{
  constructor() {}

  async getAll(): Promise<SqlExecutor[]> {
    try {
      return await sqlExecutorRepository.find();
    } catch (error: unknown) {
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async getOne(id: string): Promise<SqlExecutor> {
    try {
      const sqlExecutor = await sqlExecutorRepository.findOneBy({ id });
      if (!sqlExecutor) {
        throw ErrorHandler.NotFound("SqlExecutor not found");
      }
      return sqlExecutor;
    } catch (error: unknown) {
      if (error instanceof Error && error.message === "SqlExecutor not found") {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async create(data: Partial<SqlExecutor>): Promise<SqlExecutor> {
    try {
      return await sqlExecutorRepository.save(data);
    } catch (error: unknown) {
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async update(id: string, data: Partial<SqlExecutor>): Promise<SqlExecutor> {
    try {
      const sqlExecutor = await this.getOne(id);
      await sqlExecutorRepository.update(id, data);
      return { ...sqlExecutor, ...data };
    } catch (error: unknown) {
      if (error instanceof Error && error.message === "SqlExecutor not found") {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await this.getOne(id);
      await sqlExecutorRepository.delete(id);
    } catch (error: unknown) {
      if (error instanceof Error && error.message === "SqlExecutor not found") {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }
}
