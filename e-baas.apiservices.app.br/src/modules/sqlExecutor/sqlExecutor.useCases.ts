import { SqlExecutor } from "./entity/sql-executor.entity";
import { sql-executorRepository } from "infra/repository";
import IContractUseCases from "infra/contracts";
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from "infra/errorHandlers";

export default class SqlExecutorUseCases implements IContractUseCases<SqlExecutor> {
    constructor() {}

    async getAll(): Promise<SqlExecutor[]> {
        try {
            return await sql-executorRepository.find();
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async getOne(id: string): Promise<SqlExecutor> {
        try {
            const sql-executor = await sql-executorRepository.findOneBy({ id });
            if (!sql-executor) {
                throw ErrorHandler.NotFound("SqlExecutor not found");
            }
            return sql-executor;
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "SqlExecutor not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async create(data: Partial<SqlExecutor>): Promise<SqlExecutor> {
        try {
            return await sql-executorRepository.save(data);
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async update(id: string, data: Partial<SqlExecutor>): Promise<SqlExecutor> {
        try {
            const sql-executor = await this.getOne(id);
            await sql-executorRepository.update(id, data);
            return { ...sql-executor, ...data };
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "SqlExecutor not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await this.getOne(id);
            await sql-executorRepository.delete(id);
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "SqlExecutor not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }
}