FROM node:21-alpine

WORKDIR /usr/src/app

# Instalar dependências necessárias para sharp, bcrypt e outras packages nativas
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    gcc \
    libc6-compat \
    vips-dev \
    pkgconfig \
    cairo-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev

# Copiar package files
COPY package*.json ./
COPY yarn.lock ./

# Instalar dependências
RUN npm install --production=false

# Copiar source code
COPY . .

# Build do TypeScript
RUN npm run build

# Criar diretórios necessários
RUN mkdir -p storage logs

# Expor porta
EXPOSE 3333

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3333/health || exit 1

# Usar usuário não-root para segurança
RUN addgroup -g 1001 -S nodejs
RUN adduser -S ebaas -u 1001
RUN chown -R ebaas:nodejs /usr/src/app
USER ebaas

CMD ["npm", "run", "start"] 